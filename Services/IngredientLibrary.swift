import Foundation

// A lightweight in-memory ingredient library loaded from ingredients.txt at startup.
// Provides prefix search and category grouping.
@MainActor
final class IngredientLibrary {
    static let shared = IngredientLibrary()
    // Globally accessible static dictionary view of the library
    static var globalLibrary: [PantryCategory: [String]] { IngredientLibrary.shared.categoryToItems }
    // Embedded fallback copy of ingredients.txt (Base64-encoded)
    private static let embeddedIngredientsBase64: String = "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"


    private(set) var categoryToItems: [PantryCategory: [String]] = [:]
    private(set) var nameToCategory: [String: PantryCategory] = [:]
    private(set) var allIngredientNames: [String] = []

    private init() {
        loadFromBundledFile()
    }

    func items(for category: PantryCategory) -> [String] {
        categoryToItems[category] ?? []
    }

    func suggest(prefix: String, limit: Int = 10) -> [String] {
        if prefix.isEmpty { return [] }
        let lower = prefix.lowercased()
        // Phase 4: substring (case/diacritic insensitive) instead of prefix-only
        return allIngredientNames.filter { $0.lowercased().contains(lower) }.prefix(limit).map { String($0) }
    }

    func group(names: [String]) -> [(String, PantryCategory)] {
        names.compactMap { name in
            guard let category = nameToCategory[name] ?? nameToCategory[name.lowercased()] else { return nil }
            return (name, category)
        }
    }

    private func loadFromBundledFile() {
        // Try to load the full taxonomy from ingredients.txt at project root
        // This file should be included in the app bundle for production
        if let url = Bundle.main.url(forResource: "ingredients", withExtension: "txt"),
           let content = try? String(contentsOf: url) {
            parseIngredientsText(content)
            return
        }



        // Last resort: decode and parse embedded ingredients library
        if let data = Data(base64Encoded: Self.embeddedIngredientsBase64),
           let content = String(data: data, encoding: .utf8) {
            parseIngredientsText(content)
            return
        }

        // If needed in development on macOS: attempt local workspace paths AFTER embedded fallback (to avoid permission prompts on iOS)
        // These will not run on iOS because we've already succeeded above, but keep as a last resort for tooling.
        let absolutePath = "/Users/<USER>/Desktop/ingredient-scanner/ingredients.txt"
        if let content = try? String(contentsOfFile: absolutePath, encoding: .utf8) {
            parseIngredientsText(content)
            return
        }
        let workspacePath = "\(NSHomeDirectory())/Desktop/ingredient-scanner/ingredients.txt"
        if let content = try? String(contentsOfFile: workspacePath, encoding: .utf8) {
            parseIngredientsText(content)
            return
        }

        // If decoding and local fallbacks somehow fail, leave empty structures
        categoryToItems = [:]
        nameToCategory = [:]
        allIngredientNames = []
    }

    private func parseIngredientsText(_ text: String) {
        var currentCategory: PantryCategory = .other
        var map: [PantryCategory: [String]] = [:]
        for line in text.components(separatedBy: .newlines) {
            let trimmed = line.trimmingCharacters(in: .whitespaces)
            if trimmed.isEmpty { continue }
            if trimmed.hasPrefix("Category:") {
                let label = trimmed.replacingOccurrences(of: "Category:", with: "").trimmingCharacters(in: .whitespaces)
                currentCategory = PantryCategory(rawValue: label)
                    ?? PantryCategoryMigration.mapOldCategoryToNew(label, ingredientName: "")
                continue
            }
            if trimmed.hasPrefix("-") {
                let name = trimmed.dropFirst().trimmingCharacters(in: .whitespaces)
                guard !name.isEmpty else { continue }
                map[currentCategory, default: []].append(name)
                nameToCategory[name] = currentCategory
                nameToCategory[name.lowercased()] = currentCategory
            }
        }
        for (cat, items) in map {
            categoryToItems[cat] = Array(Set(items)).sorted { $0.localizedCaseInsensitiveCompare($1) == .orderedAscending }
        }
        allIngredientNames = categoryToItems.values.flatMap { $0 }.sorted { $0.localizedCaseInsensitiveCompare($1) == .orderedAscending }
    }
}
