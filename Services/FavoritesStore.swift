import Foundation

@MainActor
final class FavoritesStore {
    static let shared = FavoritesStore()
    private let defaults = UserDefaults.standard
    private let key = "favorites.recipes.v1"

    private init() {}

    /// Toggle favorite status. When adding, insert at the front to keep newest-first ordering.
    func toggleFavorite(id: String) {
        var arr = loadArray()
        if let idx = arr.firstIndex(of: id) {
            arr.remove(at: idx)
        } else {
            arr.insert(id, at: 0)
        }
        saveArray(arr)
    }

    func isFavorite(id: String) -> Bool {
        return loadArray().contains(id)
    }

    /// Returns favorite IDs in newest-first order
    func all() -> [String] {
        return loadArray()
    }

    // MARK: - Private

    private func loadArray() -> [String] {
        return (defaults.array(forKey: key) as? [String]) ?? []
    }

    private func saveArray(_ arr: [String]) {
        defaults.set(arr, forKey: key)
    }
}

