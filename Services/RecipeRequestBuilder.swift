import Foundation

enum RequestBuildError: Error, Equatable {
    case pantryEmpty
    case invalidConfiguration(String)
}

enum RecipeRequestBuilder {
    static func build(mode: UIMode, custom: CustomConfiguration, quick: QuickConfiguration, pantryState: PantryState, userEquipment: [String] = []) -> Result<RecipeGenerationRequest, RequestBuildError> {
        switch pantryState {
        case .empty:
            return .failure(.pantryEmpty)
        case .hasItems(let count):
            switch mode {
            case .quick:
                // Validate quick bounds
                guard (5...120).contains(quick.totalTimeMinutes) else { return .failure(.invalidConfiguration("invalid_quick_time")) }
                guard (1...6).contains(quick.numberOfDishes) else { return .failure(.invalidConfiguration("invalid_quick_dishes")) }
                let details = RequestDetails.build(from: quick, equipmentOwned: userEquipment)
                return .success(.init(mode: .quick, pantryItemCount: count, requestDetails: details))
            case .custom:
                // Validate per PRD v3 (no ingredientStrategy)
                guard !custom.selectedMeals.isEmpty else { return .failure(.invalidConfiguration("no_meals")) }
                guard (1...7).contains(custom.days) else { return .failure(.invalidConfiguration("invalid_days")) }
                for meal in custom.selectedMeals {
                    guard let cfg = custom.mealConfigurations[meal] else { return .failure(.invalidConfiguration("missing_meal_cfg_\(meal.rawValue)")) }
                    guard (5...120).contains(cfg.cookingTimeMinutes) else { return .failure(.invalidConfiguration("invalid_time_\(meal.rawValue)")) }
                    guard (1...6).contains(cfg.numberOfDishes) else { return .failure(.invalidConfiguration("invalid_dishes_\(meal.rawValue)")) }
                }
                let details = RequestDetails.build(from: custom, equipmentOwned: userEquipment)
                return .success(.init(mode: .custom, pantryItemCount: count, requestDetails: details))
            }
        case .loading:
            return .failure(.invalidConfiguration("pantry_loading"))
        case .error:
            return .failure(.invalidConfiguration("pantry_error"))
        }
    }
}

