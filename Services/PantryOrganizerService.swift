import Foundation

// MARK: - Data Structures

/// Represents an update operation for an existing ingredient
struct UpdateOp: Codable {
    let id: UUID
    let newName: String
    let newCategory: String
}

/// Represents a merge operation to consolidate duplicates
struct MergeOp: Codable {
    let winnerId: UUID
    let loserIds: [UUID]
}

/// Complete plan for cleaning up the pantry
struct CleanUpPlan: Codable {
    var updates: [UpdateOp]
    var removals: [UUID]
    var merges: [MergeOp]
}

/// Summary of organization results
struct PantryOrganizerSummary {
    var updatedCount: Int
    var removedCount: Int
    var mergedCount: Int
}

/// Errors that can occur during pantry organization
enum OrganizerError: Error, LocalizedError {
    case invalidResponse(String)
    case applicationError(String)
    case apiError(String)
    case parseError(String)
    case partialBatchSuccess(processedCount: Int, totalCount: Int)
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse(let message):
            return "Invalid response: \(message)"
        case .applicationError(let message):
            return "Application error: \(message)"
        case .apiError(let message):
            return "API error: \(message)"
        case .parseError(let message):
            return "Parse error: \(message)"
        case .partialBatchSuccess(let processedCount, let totalCount):
            return "Processed \(processedCount) of \(totalCount) items successfully"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .invalidResponse, .apiError, .parseError:
            return "Please try again later"
        case .applicationError:
            return "Please try again or contact support"
        case .partialBatchSuccess:
            return "Some items couldn't be processed. You can continue with partial results or try again"
        }
    }
}

// MARK: - PantryOrganizerService

actor PantryOrganizerService {
    private let geminiService: GeminiAPIService
    private let pantryService: PantryService
    private let batchSize = 150 // Process 100-200 items per batch
    
    init(geminiService: GeminiAPIService, pantryService: PantryService) {
        self.geminiService = geminiService
        self.pantryService = pantryService
    }
    
    /// Organize pantry items by processing them in batches with retry functionality
    func organize(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan {
        // Validate inputs
        guard !items.isEmpty else {
            throw OrganizerError.invalidResponse("No items to organize")
        }

        guard !allowedCategories.isEmpty else {
            throw OrganizerError.invalidResponse("No allowed categories provided")
        }

        // Process in batches to respect token limits
        var fullPlan = CleanUpPlan(updates: [], removals: [], merges: [])
        var processedCount = 0
        var failedBatches = 0
        var failedBatchItems: [Ingredient] = []

        // Split items into batches
        let batches = stride(from: 0, to: items.count, by: batchSize).map {
            Array(items[$0..<min($0 + batchSize, items.count)])
        }

        // Process each batch with retry logic
        for (batchIndex, batch) in batches.enumerated() {
            do {
                let batchPlan = try await processBatchWithRetry(
                    items: batch,
                    allowedCategories: allowedCategories,
                    batchIndex: batchIndex
                )
                fullPlan.updates.append(contentsOf: batchPlan.updates)
                fullPlan.removals.append(contentsOf: batchPlan.removals)
                fullPlan.merges.append(contentsOf: batchPlan.merges)
                processedCount += batch.count
            } catch {
                failedBatches += 1
                failedBatchItems.append(contentsOf: batch)
                print("❌ Failed to process batch \(batchIndex + 1) after retries: \(error)")
                // Continue with other batches for graceful degradation
            }
        }

        // Check if we have at least some results
        if processedCount == 0 {
            throw OrganizerError.apiError("Could not process any items")
        }

        // Note partial success with detailed information
        if failedBatches > 0 {
            print("⚠️ Processed \(processedCount) of \(items.count) items successfully")
            print("⚠️ Failed batches: \(failedBatches), Failed items: \(failedBatchItems.count)")
            throw OrganizerError.partialBatchSuccess(processedCount: processedCount, totalCount: items.count)
        }

        return fullPlan
    }

    /// Process a batch with retry functionality for better error resilience
    private func processBatchWithRetry(
        items: [Ingredient],
        allowedCategories: [String],
        batchIndex: Int,
        maxRetries: Int = 2
    ) async throws -> CleanUpPlan {
        var lastError: Error?

        for attempt in 0...maxRetries {
            do {
                print("🔄 Processing batch \(batchIndex + 1), attempt \(attempt + 1)")
                return try await processBatch(items: items, allowedCategories: allowedCategories)
            } catch {
                lastError = error
                print("⚠️ Batch \(batchIndex + 1) attempt \(attempt + 1) failed: \(error)")

                // Don't retry on the last attempt
                if attempt < maxRetries {
                    // Exponential backoff: wait 1s, then 2s
                    let delay = TimeInterval(1 << attempt)
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        // If we get here, all retries failed
        throw lastError ?? OrganizerError.apiError("Batch processing failed after \(maxRetries + 1) attempts")
    }
    
    /// Process a single batch of items
    private func processBatch(items: [Ingredient], allowedCategories: [String]) async throws -> CleanUpPlan {
        // Build prompt for this batch
        let prompt = buildOrganizerPrompt(items: items, allowedCategories: allowedCategories)
        
        // Call Gemini API
        let response = try await geminiService.callGeminiAPI(prompt: prompt)
        
        // Parse response into CleanUpPlan
        return try parseCleanUpPlan(response)
    }
    
    /// Build the AI prompt for pantry organization
    private func buildOrganizerPrompt(items: [Ingredient], allowedCategories: [String]) -> String {
        var prompt = """
        You are a pantry organization assistant. Clean up the following pantry items by:
        1. Fixing categories to match exactly one of these allowed categories: \(allowedCategories.joined(separator: ", "))
        2. Cleaning names by removing brand names, sizes, quantities, and marketing text
        3. Preserving meaningful descriptors (e.g., "whole milk" instead of just "milk"); allow simple plural→singular normalization when appropriate
        4. Identifying non-food items or gibberish for removal
        5. Grouping duplicate or near-duplicate items (e.g., singular/plural variants, minor spelling, brand noise) and returning merge operations
        6. If an item cannot be assigned to one of the allowed categories, suggest removal instead of guessing

        Return ONLY a single JSON object with this exact structure (no backticks, no commentary):
        {
          "updates": [
            { "id": "<UUID>", "newName": "<string>", "newCategory": "<one of allowed categories>" }
          ],
          "removals": [
            "<UUID>", "<UUID>"
          ],
          "merges": [
            { "winnerId": "<UUID>", "loserIds": ["<UUID>", "<UUID>"] }
          ]
        }

        Notes:
        - Do not include any fields other than those shown above
        - For removals, include only the UUID strings (not objects)
        - Categories must match exactly one of the allowed categories
        - Prefer merges over aggressive renaming when items are essentially the same
        """

        // Add items to prompt
        prompt += "\n\nPANTRY ITEMS:\n"
        for item in items {
            prompt += "{\"id\": \"\(item.id)\", \"name\": \"\(item.name)\", \"category\": \"\(item.category.rawValue)\"}\n"
        }
        
        return prompt
    }
    
    /// Parse the AI response into a structured CleanUpPlan
    private func parseCleanUpPlan(_ response: String) throws -> CleanUpPlan {
        // Extract JSON from response
        guard let jsonData = response.data(using: .utf8) else {
            throw OrganizerError.invalidResponse("Could not convert response to data")
        }
        
        // Decode JSON
        let decoder = JSONDecoder()
        do {
            return try decoder.decode(CleanUpPlan.self, from: jsonData)
        } catch {
            throw OrganizerError.parseError("Failed to decode CleanUpPlan: \(error.localizedDescription)")
        }
    }
    
    /// Apply the cleanup plan to the pantry with enhanced error handling and data consistency
    func apply(plan: CleanUpPlan) async throws -> PantryOrganizerSummary {
        var summary = PantryOrganizerSummary(updatedCount: 0, removedCount: 0, mergedCount: 0)
        var errors: [Error] = []

        // Apply updates in batches for atomicity and better error handling
        let updateBatches = stride(from: 0, to: plan.updates.count, by: 50).map {
            Array(plan.updates[$0..<min($0 + 50, plan.updates.count)])
        }

        for (batchIndex, batch) in updateBatches.enumerated() {
            do {
                try await applyUpdateBatchWithRetry(batch, batchIndex: batchIndex)
                summary.updatedCount += batch.count
                print("✅ Applied update batch \(batchIndex + 1) with \(batch.count) items")
            } catch {
                errors.append(error)
                print("❌ Error applying update batch \(batchIndex + 1): \(error)")
                // Continue with other operations for graceful degradation
            }
        }

        // Apply removals with error handling
        if !plan.removals.isEmpty {
            do {
                try await applyRemovalsWithRetry(plan.removals)
                summary.removedCount += plan.removals.count
                print("✅ Applied \(plan.removals.count) removals")
            } catch {
                errors.append(error)
                print("❌ Error applying removals: \(error)")
            }
        }

        // Apply merges one by one with enhanced error handling
        var successfulMerges = 0
        for (mergeIndex, merge) in plan.merges.enumerated() {
            do {
                try await applyMergeWithRetry(merge, mergeIndex: mergeIndex)
                successfulMerges += merge.loserIds.count
                print("✅ Applied merge \(mergeIndex + 1): merged \(merge.loserIds.count) items")
            } catch {
                errors.append(error)
                print("❌ Error applying merge \(mergeIndex + 1): \(error)")
                // Continue with other merges
            }
        }
        summary.mergedCount = successfulMerges

        // If we had significant errors, throw a summary error
        if !errors.isEmpty {
            let errorCount = errors.count
            let totalOperations = updateBatches.count + (plan.removals.isEmpty ? 0 : 1) + plan.merges.count

            if errorCount >= totalOperations / 2 {
                // More than half the operations failed
                throw OrganizerError.applicationError("Failed to apply \(errorCount) of \(totalOperations) operations. Partial results may be inconsistent.")
            } else {
                // Some operations failed but majority succeeded
                print("⚠️ \(errorCount) of \(totalOperations) operations failed, but majority succeeded")
            }
        }

        return summary
    }
    
    /// Apply a batch of updates with retry functionality
    private func applyUpdateBatchWithRetry(_ updates: [UpdateOp], batchIndex: Int, maxRetries: Int = 2) async throws {
        var lastError: Error?

        for attempt in 0...maxRetries {
            do {
                try await applyUpdateBatch(updates)
                return // Success
            } catch {
                lastError = error
                print("⚠️ Update batch \(batchIndex + 1) attempt \(attempt + 1) failed: \(error)")

                if attempt < maxRetries {
                    // Brief delay before retry
                    try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                }
            }
        }

        throw lastError ?? OrganizerError.applicationError("Update batch failed after \(maxRetries + 1) attempts")
    }

    /// Apply a batch of updates
    private func applyUpdateBatch(_ updates: [UpdateOp]) async throws {
        for update in updates {
            guard let category = PantryCategory(rawValue: update.newCategory) else {
                print("⚠️ Invalid category for update: \(update.newCategory)")
                continue
            }

            await pantryService.updateIngredient(
                id: update.id,
                newName: update.newName,
                newCategory: category
            )
        }
    }
    
    /// Apply removals with retry functionality
    private func applyRemovalsWithRetry(_ removals: [UUID], maxRetries: Int = 2) async throws {
        var lastError: Error?

        for attempt in 0...maxRetries {
            do {
                try await applyRemovals(removals)
                return // Success
            } catch {
                lastError = error
                print("⚠️ Removals attempt \(attempt + 1) failed: \(error)")

                if attempt < maxRetries {
                    // Brief delay before retry
                    try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                }
            }
        }

        throw lastError ?? OrganizerError.applicationError("Removals failed after \(maxRetries + 1) attempts")
    }

    /// Apply removals
    private func applyRemovals(_ removals: [UUID]) async throws {
        for id in removals {
            await pantryService.deleteIngredient(id: id)
        }
    }
    
    /// Apply a merge operation with retry functionality
    private func applyMergeWithRetry(_ merge: MergeOp, mergeIndex: Int, maxRetries: Int = 2) async throws {
        var lastError: Error?

        for attempt in 0...maxRetries {
            do {
                try await applyMerge(merge)
                return // Success
            } catch {
                lastError = error
                print("⚠️ Merge \(mergeIndex + 1) attempt \(attempt + 1) failed: \(error)")

                if attempt < maxRetries {
                    // Brief delay before retry
                    try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                }
            }
        }

        throw lastError ?? OrganizerError.applicationError("Merge failed after \(maxRetries + 1) attempts")
    }

    /// Apply a merge operation
    private func applyMerge(_ merge: MergeOp) async throws {
        // Validate that winner exists before proceeding
        guard await pantryService.ingredientExists(id: merge.winnerId) else {
            throw OrganizerError.applicationError("Winner ingredient \(merge.winnerId) not found")
        }

        // Keep the winner, remove the losers with validation
        for loserId in merge.loserIds {
            if await pantryService.ingredientExists(id: loserId) {
                await pantryService.deleteIngredient(id: loserId)
            } else {
                print("⚠️ Loser ingredient \(loserId) not found, skipping deletion")
            }
        }
        // Note: The winner ingredient remains unchanged
    }
} 