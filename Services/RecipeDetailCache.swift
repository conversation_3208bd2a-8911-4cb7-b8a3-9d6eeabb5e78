import Foundation

/// Simple in-memory cache for recipe details
@MainActor
class RecipeDetailCache: ObservableObject {
    static let shared = RecipeDetailCache()
    
    private var cache: [String: RecipeDetail] = [:]
    private let maxCacheSize = 50
    
    private init() {}
    
    /// Get cached recipe detail by title
    func getCachedDetail(for title: String) -> RecipeDetail? {
        let key = cacheKey(for: title)
        return cache[key]
    }
    
    /// Cache a recipe detail
    func cacheDetail(_ detail: RecipeDetail, for title: String) {
        let key = cacheKey(for: title)
        
        // Simple LRU: remove oldest if cache is full
        if cache.count >= maxCacheSize {
            let oldestKey = cache.keys.first
            if let oldestKey = oldestKey {
                cache.removeValue(forKey: oldestKey)
            }
        }
        
        cache[key] = detail
    }
    
    /// Clear all cached details
    func clearCache() {
        cache.removeAll()
    }
    
    /// Generate cache key from title (normalized)
    private func cacheKey(for title: String) -> String {
        return title.lowercased()
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: " ", with: "_")
    }
    
    /// Get cache statistics for debugging
    var cacheStats: (count: Int, maxSize: Int) {
        return (count: cache.count, maxSize: maxCacheSize)
    }
}
