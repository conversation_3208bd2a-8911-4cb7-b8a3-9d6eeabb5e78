import Foundation

@MainActor
struct RecipeServiceAdapter {
    let recipeService: RecipeGenerationServiceProtocol
    let pantryService: PantryService

    func generate(using request: RecipeGenerationRequest, cookingTimeMinutes: Int, authService: AuthenticationService) async throws -> [RecipeUIModel] {
        // Phase 1: Enforce pantry-only (water implicitly allowed by prompt)
        let pantryIngredients = pantryService.pantryItems.map { $0.name }
        guard !pantryIngredients.isEmpty else {
            throw RecipeGenerationError.noPantryItems
        }

        // Build preferences baseline from user profile with Phase 1 constraints
        var preferences: RecipePreferences
        if let userPrefs = authService.userPreferences {
            preferences = RecipePreferences(from: userPrefs, cookingTime: cookingTimeMinutes)

            // Phase 1: Apply profile constraints
            // - servings = familySize
            preferences.numberOfServings = userPrefs.familySize

            // - restrictions/allergies/exclusions if respectRestrictions
            if userPrefs.respectRestrictions {
                preferences.dietaryRestrictions = userPrefs.dietaryRestrictions.map { $0.rawValue }
                preferences.allergiesAndIntolerances = userPrefs.allergiesIntolerances.map { $0.rawValue }
                preferences.strictExclusions = userPrefs.strictExclusions.map { $0.rawValue }
                preferences.respectRestrictions = true
            }

            // Phase 2: Apply equipment from profile
            preferences.equipmentOwned = userPrefs.equipmentOwned
        } else {
            preferences = RecipePreferences(
                cookingTimeInMinutes: cookingTimeMinutes,
                numberOfServings: 2,
                dietaryRestrictions: [],
                allergiesAndIntolerances: [],
                strictExclusions: [],
                respectRestrictions: true
            )
        }

        // Overlay request-level preferences (cuisines, additional request, equipment)
        if let reqPrefs = request.requestDetails?.preferences {
            preferences.cuisines = reqPrefs.cuisines
            preferences.additionalRequest = reqPrefs.additionalRequest
            preferences.equipmentOwned = reqPrefs.equipmentOwned
        }

        // Call underlying service with pantry-only ingredients
        let ideas = try await recipeService.generateMealIdeas(from: pantryIngredients, preferences: preferences)

        // Phase 1: Map to UI models with pantry-only semantics
        // additionalIngredients must be empty (pantry-only enforcement)
        let uiModels = ideas.map { idea in
            let r = idea.recipe
            return RecipeUIModel(
                id: r.id.uuidString,
                title: r.title,
                subtitle: r.description,
                estimatedTime: r.cookingTimeInMinutes,
                imageURL: nil,
                ingredientsFromPantry: filterPantryIngredients(r.ingredients, pantryIngredients: pantryIngredients),
                additionalIngredients: [], // Phase 1: Always empty for pantry-only mode
                difficulty: r.difficulty.rawValue
            )
        }

        // Phase 1: Post-generation sanity check for profile constraints
        return filterCompatibleRecipes(uiModels, userPreferences: authService.userPreferences)
    }

    // MARK: - Private Helper Methods

    /// Filter recipe ingredients to only include those available in pantry
    private func filterPantryIngredients(_ recipeIngredients: [String], pantryIngredients: [String]) -> [String] {
        let pantrySet = Set(pantryIngredients.map { $0.lowercased() })
        return recipeIngredients.filter { ingredient in
            pantrySet.contains(ingredient.lowercased()) || ingredient.lowercased() == "water"
        }
    }

    /// Phase 1: Post-generation sanity check for profile constraints
    private func filterCompatibleRecipes(_ recipes: [RecipeUIModel], userPreferences: UserPreferences?) -> [RecipeUIModel] {
        guard let userPrefs = userPreferences, userPrefs.respectRestrictions else {
            return recipes
        }

        // Build blocked terms list
        var blockedTerms: [String] = []
        blockedTerms.append(contentsOf: userPrefs.dietaryRestrictions.map { $0.rawValue })
        blockedTerms.append(contentsOf: userPrefs.allergiesIntolerances.map { $0.rawValue })
        blockedTerms.append(contentsOf: userPrefs.strictExclusions.map { $0.rawValue })
        let blockedTermsSet = Set(blockedTerms.map { $0.lowercased() })

        return recipes.filter { recipe in
            let titleText = recipe.title.lowercased()
            let subtitleText = (recipe.subtitle ?? "").lowercased()
            let ingredientsText = (recipe.ingredientsFromPantry ?? []).joined(separator: " ").lowercased()
            let allText = titleText + " " + subtitleText + " " + ingredientsText

            // Check if any blocked term appears in the recipe text
            for term in blockedTermsSet {
                if allText.contains(term) {
                    return false
                }
            }
            return true
        }
    }

    // MARK: - Phase 3: Regenerate Batch
    func regenerateBatch(_ regen: RegenerateRequest, authService: AuthenticationService) async throws -> [RecipeUIModel] {
        // Build preferences baseline similar to generate(); reuse pantry-only enforcement
        let pantryIngredients = pantryService.pantryItems.map { $0.name }
        guard !pantryIngredients.isEmpty else { throw RecipeGenerationError.noPantryItems }

        var preferences: RecipePreferences
        if let userPrefs = authService.userPreferences {
            preferences = RecipePreferences(from: userPrefs, cookingTime: 30)
            preferences.numberOfServings = userPrefs.familySize
            if userPrefs.respectRestrictions {
                preferences.dietaryRestrictions = userPrefs.dietaryRestrictions.map { $0.rawValue }
                preferences.allergiesAndIntolerances = userPrefs.allergiesIntolerances.map { $0.rawValue }
                preferences.strictExclusions = userPrefs.strictExclusions.map { $0.rawValue }
                preferences.respectRestrictions = true
            }
            // Phase 2: Apply equipment from profile
            preferences.equipmentOwned = userPrefs.equipmentOwned
        } else {
            preferences = RecipePreferences(cookingTimeInMinutes: 30, numberOfServings: 2, dietaryRestrictions: [])
        }
        preferences.cuisines = regen.cuisines
        preferences.additionalRequest = regen.additionalRequest

        // First attempt
        var ideas = try await recipeService.generateMealIdeas(from: pantryIngredients, preferences: preferences)
        var replacements = mapIdeasToReplacements(ideas: ideas, exclusions: regen.exclusions.titles, needed: regen.targetSlots.count)

        // Retry once if fewer than needed
        if replacements.count < regen.targetSlots.count {
            ideas = try await recipeService.generateMealIdeas(from: pantryIngredients, preferences: preferences)
            let additional = mapIdeasToReplacements(ideas: ideas, exclusions: regen.exclusions.titles + replacements.map { $0.title }, needed: regen.targetSlots.count - replacements.count)
            replacements.append(contentsOf: additional)
        }

        return replacements
    }

    // Simple similarity-based filtering and mapping
    private func mapIdeasToReplacements(ideas: [RecipeIdea], exclusions: [String], needed: Int) -> [RecipeUIModel] {
        let excluded = exclusions.map { $0.lowercased() }
        var results: [RecipeUIModel] = []
        for idea in ideas {
            let r = idea.recipe
            let title = r.title
            if excluded.contains(where: { similar($0, title.lowercased()) >= 0.85 }) { continue }
            let model = RecipeUIModel(
                id: r.id.uuidString,
                title: r.title,
                subtitle: r.description,
                estimatedTime: r.cookingTimeInMinutes,
                imageURL: nil,
                ingredientsFromPantry: filterPantryIngredients(r.ingredients, pantryIngredients: pantryService.pantryItems.map { $0.name }),
                additionalIngredients: [],
                difficulty: r.difficulty.rawValue
            )
            results.append(model)
            if results.count >= needed { break }
        }
        return results
    }

    // Normalized token similarity (very simple Jaccard index)
    private func similar(_ a: String, _ b: String) -> Double {
        let tokensA = Set(a.split(separator: " ").map { String($0) })
        let tokensB = Set(b.split(separator: " ").map { String($0) })
        let inter = tokensA.intersection(tokensB).count
        let union = tokensA.union(tokensB).count
        if union == 0 { return 0 }
        return Double(inter) / Double(union)
    }
}

