# Changelog

## Unreleased

- Removed legacy Gemini batch processing paths and prompts:
  - Deleted BatchGeminiProcessingView.swift and BatchGeminiProcessingViewModel.swift
  - Deleted BatchProcessingView(.swift) and BatchProcessingViewModel(.swift)
  - Deleted BatchVisionResultsView(.swift) and BatchVisionResultsViewModel(.swift)
  - Removed GeminiAPIService.extractIngredients(from:) and .analyzeIngredients(in:)
  - Removed deprecated AppRoute cases and navigation helpers for batchProcessing, batchVisionResults, and batchGeminiProcessing
  - Updated AppCoordinator to use only the unified staging → results flow
  - Updated tests accordingly (NavigationCoordinatorTests, AppRouteTests)

Migration notes:
- Use StagingView/StagingViewModel for the complete Vision → Gemini pipeline
- For results: call NavigationCoordinator.navigateToResults(ingredients:)
- Any deep links or code paths using legacy batch routes should be updated to staging

