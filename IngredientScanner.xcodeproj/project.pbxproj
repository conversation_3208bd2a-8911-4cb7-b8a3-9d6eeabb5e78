// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0284549EC8FD2A757D22C0FB /* FirebaseFirestore in Frameworks */ = {isa = PBXBuildFile; productRef = B4B07A517E72BCF7376D449A /* FirebaseFirestore */; };
		04F4BC4DFB7E9DCA9E050276 /* RecipeDetailCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD5874C638A51E908AA3C197 /* RecipeDetailCache.swift */; };
		076E088D06AEA68BF880E443 /* EquipmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CBB03341FB7787DBE4C01335 /* EquipmentView.swift */; };
		07CA588ECA8D0CC719AEE696 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = D59E35DCDAE975A10BC98623 /* FirebaseAnalytics */; };
		10DB2E5C2CC15BC608773B0B /* RecipeGrouper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 561B59FB1C991C51A3AF05DB /* RecipeGrouper.swift */; };
		11C58082195D9D6D63494CFC /* GroupedResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51F07E8F5B2F774E45C5AD58 /* GroupedResultsView.swift */; };
		11F4D8DE14658CEE89A81B24 /* RegenerateRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 416FE6AD741760160A09D36A /* RegenerateRequest.swift */; };
		1602CCC1FCD042DA353BFB5D /* ErrorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 65C9D20B21C9792C0477E9CB /* ErrorView.swift */; };
		16947ACBFFE296C4C5608521 /* ImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = C093F1FD032970F0D124F474 /* ImagePicker.swift */; };
		190112BDFD568D7BD1682DCA /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 3C4267156C55BCA053A6D391 /* Assets.xcassets */; };
		1A3D422E366B31A3F4C92CA7 /* PantryStateWarning.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0F436AB8DE0B60A131EAA60 /* PantryStateWarning.swift */; };
		1C514D8C68BD8C0DA425F4B0 /* ConcurrencyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 618BC01DC46E43C192A972EA /* ConcurrencyManager.swift */; };
		2009917FD1AE77EF5C26151D /* PantryViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */; };
		257DCFADF4F8AA5375CEAAE6 /* QuickResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 203CBC78A5DD35F560757709 /* QuickResultsView.swift */; };
		286D3AD60207B07E1922ED46 /* Recipe.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE22904B43C38F5DA2C1BCCE /* Recipe.swift */; };
		2CD4FB43C1F24D0A6C270602 /* RecipeUIModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00C6D31A5B45C429D2C7BC73 /* RecipeUIModel.swift */; };
		300FF772D541C1E8D839037D /* ResultsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 58F7E951E15E903C3F2DA88E /* ResultsView.swift */; };
		350020676BBFA2E592C4CE7A /* AuthenticationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 002990977B33054F36A3499D /* AuthenticationService.swift */; };
		3AFFA6126A9555091B68EAC6 /* SwiftDataModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = D4867FFC3C03EA927A2A4619 /* SwiftDataModels.swift */; };
		3BBAF884691CE8BCAC30AFE1 /* RecipesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98B8CC92CB9B5D9E94B23193 /* RecipesView.swift */; };
		3BF985B6E33165CCCAB97988 /* MultiImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */; };
		3CEEAA16C0A38AD1F62E30E0 /* PantryService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DFC23B81C1836632A9C0C7D1 /* PantryService.swift */; };
		3E55F8A163B930B0C10DF7C3 /* ExpirationNotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B0B1D1EECDE31EBE316148E /* ExpirationNotificationManager.swift */; };
		405B7FF8557B6E9D50708FB4 /* ViewState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 35CDF50BB12C724D6BBE8CC2 /* ViewState.swift */; };
		40FEB17B88C5F3765C569E97 /* RemoteConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FD4633A895C41348C22C35D /* RemoteConfiguration.swift */; };
		456E8CB6D048D357117FDAE1 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44C234B0CF86ED71B89397C0 /* LoadingView.swift */; };
		45D2DE7DBB798692C6F82957 /* SignInView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FFE3881D55D069B1B730E46 /* SignInView.swift */; };
		465AA90B58DBF295DF9729FB /* PantryCategory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */; };
		46D1AA5FBF4641ECC38CAB93 /* GeneratedRecipeDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */; };
		470B030CFE6F3D8EF8AF9C97 /* App.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C6BAB000A706022E5DE4311 /* App.swift */; };
		4CAC1DE461273018EEC02DA7 /* ResultsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */; };
		4DDFE5D0B3F7B40FEFBA78BD /* PantryStateProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDAD84D6504FD42EA06C4AFD /* PantryStateProvider.swift */; };
		4FD023AD5DA226B136291273 /* ToastView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2320D0A065905E7850A024EC /* ToastView.swift */; };
		546FD3797670C44238850D6F /* ExpirationAlertManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB908385AD7D068AB5312E67 /* ExpirationAlertManager.swift */; };
		56D28E25E361C9BED25A9AE7 /* EnhancedRecipeCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C5D3C601DCB6D96733C2AF3 /* EnhancedRecipeCard.swift */; };
		57C0F9F47D4919B96AB3C9A9 /* FavoritesStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = DBF14D5276C786D79A3296EA /* FavoritesStore.swift */; };
		590EF66376A8348CBBBF4BCF /* GeminiAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */; };
		5C0A0B8D64D2DE136D8CBE73 /* DietaryRestrictionsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E234451DACF3A41DDE0F90B6 /* DietaryRestrictionsView.swift */; };
		5CDD243A8D50CF3B1D79508C /* PreferencesEditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B1D16C4399D928ED75C0D406 /* PreferencesEditView.swift */; };
		5ED14FC0EC3F3AA2E63FD535 /* APIKeys.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6E814F10D859D0594F1E3C2 /* APIKeys.swift */; };
		5FA62A0FB480B7DD1ACFFBFC /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 634B5D2304CD06FF2A54A345 /* ProfileView.swift */; };
		6C6A417E7F5E0972303323D6 /* DebugViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64498B16CC20397CD1568211 /* DebugViewModel.swift */; };
		728037D12D75D4B900849FE8 /* AsyncImagePreparationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2AE13D94EDA6D7C6CF36F861 /* AsyncImagePreparationService.swift */; };
		73076B2B7A47E522AAECBD7F /* RemoteConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = EACA497F9E6F77C9E4D26016 /* RemoteConfigurationManager.swift */; };
		74F5ADC7DEBC005347DFF87A /* SwiftDataStorageService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 81535A450F231A7A47014D5C /* SwiftDataStorageService.swift */; };
		76672B856A0F24B02726596D /* SummaryGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 508254809ABE98C8FD2CD764 /* SummaryGenerator.swift */; };
		78C944EA1DE275CA98A3A83A /* AllergiesIntolerancesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 14C94D6C263677D89C9261EF /* AllergiesIntolerancesView.swift */; };
		7A3011B5F41A0480B47E8D1C /* StagingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D8597742EEEA039E4F43ED6 /* StagingView.swift */; };
		804B6C0A9D4933B2F8818113 /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = ABE43B4277A5DC9DFB4721A7 /* FirebaseAuth */; };
		82CFCA724ABD5520D70A210B /* GenerateButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49FCA1EACFEE3F7BA83C1151 /* GenerateButton.swift */; };
		83EC4916030D1DE92209B3FB /* FamilySizeView.swift in Sources */ = {isa = PBXBuildFile; fileRef = F0CA4798A814D79944DB5ADA /* FamilySizeView.swift */; };
		84995C5F1A57FF917F21854C /* PreparedImage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C4BB6A173DBA2A26848EBEB /* PreparedImage.swift */; };
		88D2F764123B52301A3B70B8 /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */; };
		89AFB66F259BE7565A5D1801 /* DebugView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 79D1F4365F0010CABC1CADF7 /* DebugView.swift */; };
		8C6699499ED137FE3BBEDE00 /* StagingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B01392334341FB8E206C523 /* StagingViewModel.swift */; };
		8E3182790C46308A760BF69D /* RecipeRequestBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = FE2E2AED4CE768FFB8719125 /* RecipeRequestBuilder.swift */; };
		929C5362403990D168CD15BC /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8AE611CBEB6F9C65CA8F9BF6 /* GoogleService-Info.plist */; };
		9634C6A0A790DFCF8DB74308 /* PantryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E09DF5AB9B43F2B5940B387E /* PantryView.swift */; };
		9980C88FF2EF298D70231142 /* ModeSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = 601D5438388977111C07153B /* ModeSelector.swift */; };
		9AAB43E55D0D950A9774FB0D /* RecipeCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B388B3CA95BA26238131006 /* RecipeCardView.swift */; };
		9AC0D19AA92FEA4636B57E8D /* Ingredient.swift in Sources */ = {isa = PBXBuildFile; fileRef = C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */; };
		9B8AA968C7F1DBC83CB101DD /* StrictExclusionsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B900EC49B4467C5908ADB0E /* StrictExclusionsView.swift */; };
		9D480EEED0BF61466A814CC5 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B23ACFF89F562B95D79832BE /* SettingsView.swift */; };
		9F593E6A75E3601FB6CA8770 /* FirestoreError.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0BF0FCC9FBAD1F55D36784E /* FirestoreError.swift */; };
		A13ABB8AE497C0CF26649E19 /* ConfigurationSummaryCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A503727A85F13B70F4996A5 /* ConfigurationSummaryCard.swift */; };
		A9EE505F6BA66751830BC279 /* UserPreferences.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB4EE67748A5F44F137361C2 /* UserPreferences.swift */; };
		AC5A62C1B1C0B0F554DC12F6 /* RecipeGenerationTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD838F051CCB17441EB36057 /* RecipeGenerationTypes.swift */; };
		ADD1FCE71785114E258ECA89 /* PantryOrganizerService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 54911ABF20CE85EEE21D36B4 /* PantryOrganizerService.swift */; };
		AEE1492DE07FCE6AEB73AC6A /* EquipmentChipsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = C2B6872E34F7CB8A70082455 /* EquipmentChipsView.swift */; };
		AF1C7E872F143AB22E28AFEB /* DisplayError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 15BA12893C06D41D7F40712F /* DisplayError.swift */; };
		B4C42ED1628DA6117B118113 /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = C7CA2B5F33DE3455FFB8FD33 /* GoogleSignIn */; };
		BDBFE1EE64AC1ACE45B2E9E8 /* api-keys.plist in Resources */ = {isa = PBXBuildFile; fileRef = 9BF94B7ADBCEE860F7B723CC /* api-keys.plist */; };
		BE82E83A77935AA118FD1AA6 /* TelemetryEventBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BF734808AFF0C664EA566A1 /* TelemetryEventBuilder.swift */; };
		BF4FB6EDD3523DB2EA76C06B /* OptimizedNetworkService.swift in Sources */ = {isa = PBXBuildFile; fileRef = C232D41B35C988DB1ED2C337 /* OptimizedNetworkService.swift */; };
		C93D1E772CC97F0DFA65CD27 /* Haptics.swift in Sources */ = {isa = PBXBuildFile; fileRef = F051D9F4E367F48AC03E0B61 /* Haptics.swift */; };
		CC1C55DA7952A3D044AC8245 /* PlanStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F8D4B506DEF312B02334F4C /* PlanStore.swift */; };
		D2694E9BF7B54C29F8F913BE /* RegenerateModeState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55ACDE8516C85BC6B4D16CC7 /* RegenerateModeState.swift */; };
		D2CB3623DB80D90596DFAFB8 /* RecipeCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1F1322C3C8EB8275E9862E22 /* RecipeCard.swift */; };
		D79545A53384C3A69227721F /* UserProfileService.swift in Sources */ = {isa = PBXBuildFile; fileRef = E45162584291B28B845255C8 /* UserProfileService.swift */; };
		D86249C65F27BEE3D39041E4 /* RecipeDetail.swift in Sources */ = {isa = PBXBuildFile; fileRef = BFD881A4E73B5A047F08F6EB /* RecipeDetail.swift */; };
		DC6AC64160026E6BE9CA4292 /* ProcessingScreen.swift in Sources */ = {isa = PBXBuildFile; fileRef = D561CE9DD4BF726A0517475C /* ProcessingScreen.swift */; };
		DEFA83DECCA0F8397BBA36DA /* RecipeServiceAdapter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D76E654E89C20F29237B9984 /* RecipeServiceAdapter.swift */; };
		E684CC98841467E1D92339FE /* GoogleVisionAPIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA27F2449FA133E6C7BD796D /* GoogleVisionAPIService.swift */; };
		E6D16E11F54A7B3AC607A39C /* RecipeGeneratorViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */; };
		EC9C0184D659F23C2A4D1112 /* RecipeGenerationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = CF40FDE398B1A6CE1FD2B0B7 /* RecipeGenerationService.swift */; };
		EDB1FCBE871A2FC42E1BB1B0 /* RecipeGeneratorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */; };
		EEED6D2A397A1A7701CA5D9C /* RecipeGenerationRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D0D2194CA4CE570714310BB /* RecipeGenerationRequest.swift */; };
		EFEA08C3450FB79F8941B2F8 /* NotificationsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD58077E9EA0CB3902A4AD44 /* NotificationsView.swift */; };
		F34EAA77130AA93558A20E26 /* IngredientLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6C33700D0F38BFB26D4F150 /* IngredientLibrary.swift */; };
		F5CE6AFD5A60E98F7FA9586E /* APIProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59F5CB5CD990DF3795FBEA8D /* APIProtocols.swift */; };
		F97E1CE239E7C4724DABF792 /* RecipesViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5DDDD7E86CC44475BCCD2D93 /* RecipesViewModel.swift */; };
		F9A41FBACF9CCA4A42169055 /* ServiceContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3CA085D99E48956579051C8A /* ServiceContainer.swift */; };
		FF18CF88C55BAAED3384A5A3 /* TelemetryService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 67762E49563B93104A5F83EC /* TelemetryService.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		002990977B33054F36A3499D /* AuthenticationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationService.swift; sourceTree = "<group>"; };
		00C6D31A5B45C429D2C7BC73 /* RecipeUIModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeUIModel.swift; sourceTree = "<group>"; };
		0B01392334341FB8E206C523 /* StagingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StagingViewModel.swift; sourceTree = "<group>"; };
		0B900EC49B4467C5908ADB0E /* StrictExclusionsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrictExclusionsView.swift; sourceTree = "<group>"; };
		11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiImagePicker.swift; sourceTree = "<group>"; };
		14C94D6C263677D89C9261EF /* AllergiesIntolerancesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AllergiesIntolerancesView.swift; sourceTree = "<group>"; };
		15BA12893C06D41D7F40712F /* DisplayError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DisplayError.swift; sourceTree = "<group>"; };
		176398FEADBD4CF189075941 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		1A503727A85F13B70F4996A5 /* ConfigurationSummaryCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationSummaryCard.swift; sourceTree = "<group>"; };
		1F1322C3C8EB8275E9862E22 /* RecipeCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeCard.swift; sourceTree = "<group>"; };
		203CBC78A5DD35F560757709 /* QuickResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuickResultsView.swift; sourceTree = "<group>"; };
		2320D0A065905E7850A024EC /* ToastView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ToastView.swift; sourceTree = "<group>"; };
		2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeneratedRecipeDetailView.swift; sourceTree = "<group>"; };
		2AE13D94EDA6D7C6CF36F861 /* AsyncImagePreparationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AsyncImagePreparationService.swift; sourceTree = "<group>"; };
		2C5D3C601DCB6D96733C2AF3 /* EnhancedRecipeCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedRecipeCard.swift; sourceTree = "<group>"; };
		2D0D2194CA4CE570714310BB /* RecipeGenerationRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGenerationRequest.swift; sourceTree = "<group>"; };
		2F8D4B506DEF312B02334F4C /* PlanStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlanStore.swift; sourceTree = "<group>"; };
		35CDF50BB12C724D6BBE8CC2 /* ViewState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewState.swift; sourceTree = "<group>"; };
		3C4267156C55BCA053A6D391 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		3C6BAB000A706022E5DE4311 /* App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = App.swift; sourceTree = "<group>"; };
		3CA085D99E48956579051C8A /* ServiceContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ServiceContainer.swift; sourceTree = "<group>"; };
		416FE6AD741760160A09D36A /* RegenerateRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegenerateRequest.swift; sourceTree = "<group>"; };
		44C234B0CF86ED71B89397C0 /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		49FCA1EACFEE3F7BA83C1151 /* GenerateButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenerateButton.swift; sourceTree = "<group>"; };
		4BF734808AFF0C664EA566A1 /* TelemetryEventBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TelemetryEventBuilder.swift; sourceTree = "<group>"; };
		508254809ABE98C8FD2CD764 /* SummaryGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SummaryGenerator.swift; sourceTree = "<group>"; };
		51F07E8F5B2F774E45C5AD58 /* GroupedResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GroupedResultsView.swift; sourceTree = "<group>"; };
		54911ABF20CE85EEE21D36B4 /* PantryOrganizerService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryOrganizerService.swift; sourceTree = "<group>"; };
		55ACDE8516C85BC6B4D16CC7 /* RegenerateModeState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegenerateModeState.swift; sourceTree = "<group>"; };
		561B59FB1C991C51A3AF05DB /* RecipeGrouper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGrouper.swift; sourceTree = "<group>"; };
		58F7E951E15E903C3F2DA88E /* ResultsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsView.swift; sourceTree = "<group>"; };
		59F5CB5CD990DF3795FBEA8D /* APIProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIProtocols.swift; sourceTree = "<group>"; };
		5DDDD7E86CC44475BCCD2D93 /* RecipesViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipesViewModel.swift; sourceTree = "<group>"; };
		601D5438388977111C07153B /* ModeSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModeSelector.swift; sourceTree = "<group>"; };
		618BC01DC46E43C192A972EA /* ConcurrencyManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConcurrencyManager.swift; sourceTree = "<group>"; };
		634B5D2304CD06FF2A54A345 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		64498B16CC20397CD1568211 /* DebugViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebugViewModel.swift; sourceTree = "<group>"; };
		65C9D20B21C9792C0477E9CB /* ErrorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ErrorView.swift; sourceTree = "<group>"; };
		67762E49563B93104A5F83EC /* TelemetryService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TelemetryService.swift; sourceTree = "<group>"; };
		6D8597742EEEA039E4F43ED6 /* StagingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StagingView.swift; sourceTree = "<group>"; };
		70D683F0BF9B783BB0F51EE6 /* IngredientScanner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = IngredientScanner.entitlements; sourceTree = "<group>"; };
		79D1F4365F0010CABC1CADF7 /* DebugView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebugView.swift; sourceTree = "<group>"; };
		7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppCoordinator.swift; sourceTree = "<group>"; };
		81535A450F231A7A47014D5C /* SwiftDataStorageService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftDataStorageService.swift; sourceTree = "<group>"; };
		84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryCategory.swift; sourceTree = "<group>"; };
		8AE611CBEB6F9C65CA8F9BF6 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		8B0B1D1EECDE31EBE316148E /* ExpirationNotificationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpirationNotificationManager.swift; sourceTree = "<group>"; };
		96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeminiAPIService.swift; sourceTree = "<group>"; };
		98B8CC92CB9B5D9E94B23193 /* RecipesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipesView.swift; sourceTree = "<group>"; };
		9B388B3CA95BA26238131006 /* RecipeCardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeCardView.swift; sourceTree = "<group>"; };
		9BF94B7ADBCEE860F7B723CC /* api-keys.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = "api-keys.plist"; sourceTree = "<group>"; };
		9C4BB6A173DBA2A26848EBEB /* PreparedImage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreparedImage.swift; sourceTree = "<group>"; };
		9FD4633A895C41348C22C35D /* RemoteConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteConfiguration.swift; sourceTree = "<group>"; };
		9FFE3881D55D069B1B730E46 /* SignInView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SignInView.swift; sourceTree = "<group>"; };
		A6C33700D0F38BFB26D4F150 /* IngredientLibrary.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IngredientLibrary.swift; sourceTree = "<group>"; };
		A6E814F10D859D0594F1E3C2 /* APIKeys.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIKeys.swift; sourceTree = "<group>"; };
		AB4EE67748A5F44F137361C2 /* UserPreferences.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserPreferences.swift; sourceTree = "<group>"; };
		AE22904B43C38F5DA2C1BCCE /* Recipe.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Recipe.swift; sourceTree = "<group>"; };
		B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsViewModel.swift; sourceTree = "<group>"; };
		B1D16C4399D928ED75C0D406 /* PreferencesEditView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreferencesEditView.swift; sourceTree = "<group>"; };
		B23ACFF89F562B95D79832BE /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		BD838F051CCB17441EB36057 /* RecipeGenerationTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGenerationTypes.swift; sourceTree = "<group>"; };
		BDAD84D6504FD42EA06C4AFD /* PantryStateProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryStateProvider.swift; sourceTree = "<group>"; };
		BFD881A4E73B5A047F08F6EB /* RecipeDetail.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeDetail.swift; sourceTree = "<group>"; };
		C093F1FD032970F0D124F474 /* ImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePicker.swift; sourceTree = "<group>"; };
		C0BF0FCC9FBAD1F55D36784E /* FirestoreError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirestoreError.swift; sourceTree = "<group>"; };
		C0F436AB8DE0B60A131EAA60 /* PantryStateWarning.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryStateWarning.swift; sourceTree = "<group>"; };
		C232D41B35C988DB1ED2C337 /* OptimizedNetworkService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OptimizedNetworkService.swift; sourceTree = "<group>"; };
		C2B6872E34F7CB8A70082455 /* EquipmentChipsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EquipmentChipsView.swift; sourceTree = "<group>"; };
		C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Ingredient.swift; sourceTree = "<group>"; };
		CBB03341FB7787DBE4C01335 /* EquipmentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EquipmentView.swift; sourceTree = "<group>"; };
		CF40FDE398B1A6CE1FD2B0B7 /* RecipeGenerationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGenerationService.swift; sourceTree = "<group>"; };
		D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = IngredientScanner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D4867FFC3C03EA927A2A4619 /* SwiftDataModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftDataModels.swift; sourceTree = "<group>"; };
		D561CE9DD4BF726A0517475C /* ProcessingScreen.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProcessingScreen.swift; sourceTree = "<group>"; };
		D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGeneratorView.swift; sourceTree = "<group>"; };
		D76E654E89C20F29237B9984 /* RecipeServiceAdapter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeServiceAdapter.swift; sourceTree = "<group>"; };
		D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryViewModel.swift; sourceTree = "<group>"; };
		DA27F2449FA133E6C7BD796D /* GoogleVisionAPIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoogleVisionAPIService.swift; sourceTree = "<group>"; };
		DBF14D5276C786D79A3296EA /* FavoritesStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FavoritesStore.swift; sourceTree = "<group>"; };
		DD5874C638A51E908AA3C197 /* RecipeDetailCache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeDetailCache.swift; sourceTree = "<group>"; };
		DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeGeneratorViewModel.swift; sourceTree = "<group>"; };
		DFC23B81C1836632A9C0C7D1 /* PantryService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryService.swift; sourceTree = "<group>"; };
		E09DF5AB9B43F2B5940B387E /* PantryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PantryView.swift; sourceTree = "<group>"; };
		E234451DACF3A41DDE0F90B6 /* DietaryRestrictionsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DietaryRestrictionsView.swift; sourceTree = "<group>"; };
		E45162584291B28B845255C8 /* UserProfileService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfileService.swift; sourceTree = "<group>"; };
		EACA497F9E6F77C9E4D26016 /* RemoteConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RemoteConfigurationManager.swift; sourceTree = "<group>"; };
		F051D9F4E367F48AC03E0B61 /* Haptics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Haptics.swift; sourceTree = "<group>"; };
		F0CA4798A814D79944DB5ADA /* FamilySizeView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FamilySizeView.swift; sourceTree = "<group>"; };
		FB908385AD7D068AB5312E67 /* ExpirationAlertManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpirationAlertManager.swift; sourceTree = "<group>"; };
		FD58077E9EA0CB3902A4AD44 /* NotificationsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationsView.swift; sourceTree = "<group>"; };
		FE2E2AED4CE768FFB8719125 /* RecipeRequestBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecipeRequestBuilder.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0786AF5BB94AD8A44FA2C5E8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				804B6C0A9D4933B2F8818113 /* FirebaseAuth in Frameworks */,
				0284549EC8FD2A757D22C0FB /* FirebaseFirestore in Frameworks */,
				07CA588ECA8D0CC719AEE696 /* FirebaseAnalytics in Frameworks */,
				B4C42ED1628DA6117B118113 /* GoogleSignIn in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1FC412429FB925EF376F80A6 /* 1_ImageCapture */ = {
			isa = PBXGroup;
			children = (
				6D8597742EEEA039E4F43ED6 /* StagingView.swift */,
				0B01392334341FB8E206C523 /* StagingViewModel.swift */,
			);
			path = 1_ImageCapture;
			sourceTree = "<group>";
		};
		3B995C1EF162DAD5B2491BF9 /* Profile */ = {
			isa = PBXGroup;
			children = (
				14C94D6C263677D89C9261EF /* AllergiesIntolerancesView.swift */,
				E234451DACF3A41DDE0F90B6 /* DietaryRestrictionsView.swift */,
				C2B6872E34F7CB8A70082455 /* EquipmentChipsView.swift */,
				CBB03341FB7787DBE4C01335 /* EquipmentView.swift */,
				65C9D20B21C9792C0477E9CB /* ErrorView.swift */,
				F0CA4798A814D79944DB5ADA /* FamilySizeView.swift */,
				44C234B0CF86ED71B89397C0 /* LoadingView.swift */,
				FD58077E9EA0CB3902A4AD44 /* NotificationsView.swift */,
				B1D16C4399D928ED75C0D406 /* PreferencesEditView.swift */,
				634B5D2304CD06FF2A54A345 /* ProfileView.swift */,
				B23ACFF89F562B95D79832BE /* SettingsView.swift */,
				9FFE3881D55D069B1B730E46 /* SignInView.swift */,
				0B900EC49B4467C5908ADB0E /* StrictExclusionsView.swift */,
				2320D0A065905E7850A024EC /* ToastView.swift */,
			);
			path = Profile;
			sourceTree = "<group>";
		};
		40D60551B89A5B09AD08F353 /* RecipeGenerator */ = {
			isa = PBXGroup;
			children = (
				2584E995A79858BB53E06153 /* GeneratedRecipeDetailView.swift */,
				9B388B3CA95BA26238131006 /* RecipeCardView.swift */,
				D6F89EB82B149F68E8B504FA /* RecipeGeneratorView.swift */,
				DF6E326DF3C70BF39313BBD0 /* RecipeGeneratorViewModel.swift */,
				60EB3E1F83D12B0C782679EA /* Components */,
				4D198BA74D55C5C5F2F97A2D /* Results */,
			);
			path = RecipeGenerator;
			sourceTree = "<group>";
		};
		4D198BA74D55C5C5F2F97A2D /* Results */ = {
			isa = PBXGroup;
			children = (
				2C5D3C601DCB6D96733C2AF3 /* EnhancedRecipeCard.swift */,
				51F07E8F5B2F774E45C5AD58 /* GroupedResultsView.swift */,
				203CBC78A5DD35F560757709 /* QuickResultsView.swift */,
				1F1322C3C8EB8275E9862E22 /* RecipeCard.swift */,
			);
			path = Results;
			sourceTree = "<group>";
		};
		60EB3E1F83D12B0C782679EA /* Components */ = {
			isa = PBXGroup;
			children = (
				1A503727A85F13B70F4996A5 /* ConfigurationSummaryCard.swift */,
				49FCA1EACFEE3F7BA83C1151 /* GenerateButton.swift */,
				601D5438388977111C07153B /* ModeSelector.swift */,
				C0F436AB8DE0B60A131EAA60 /* PantryStateWarning.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		72C95A01F0CC48629DF9E9B1 /* 2_Results */ = {
			isa = PBXGroup;
			children = (
				D561CE9DD4BF726A0517475C /* ProcessingScreen.swift */,
			);
			path = 2_Results;
			sourceTree = "<group>";
		};
		75DE33128F7D036CC2DBC4AA /* 3_Results */ = {
			isa = PBXGroup;
			children = (
				58F7E951E15E903C3F2DA88E /* ResultsView.swift */,
				B02FFC8EB3DFEB02AD57E0F2 /* ResultsViewModel.swift */,
			);
			path = 3_Results;
			sourceTree = "<group>";
		};
		777F36B8DA01C8AB0E61EDA1 /* Debug */ = {
			isa = PBXGroup;
			children = (
				79D1F4365F0010CABC1CADF7 /* DebugView.swift */,
				64498B16CC20397CD1568211 /* DebugViewModel.swift */,
			);
			path = Debug;
			sourceTree = "<group>";
		};
		8AF856DE78DD25E25BA3690A /* Pantry */ = {
			isa = PBXGroup;
			children = (
				E09DF5AB9B43F2B5940B387E /* PantryView.swift */,
				D94EBA0AA79E38BB545F9F61 /* PantryViewModel.swift */,
			);
			path = Pantry;
			sourceTree = "<group>";
		};
		A1F9EAED5F5762EE342BAE4F /* Coordinator */ = {
			isa = PBXGroup;
			children = (
				7CAF8BF954BEC15E0AA2C354 /* AppCoordinator.swift */,
			);
			path = Coordinator;
			sourceTree = "<group>";
		};
		A3C06954CFBF169AC85CF7F6 /* Models */ = {
			isa = PBXGroup;
			children = (
				15BA12893C06D41D7F40712F /* DisplayError.swift */,
				C354DB04ECE65CBF0CAE1FF5 /* Ingredient.swift */,
				84B3CC06B1FB6E25E979F9D9 /* PantryCategory.swift */,
				AE22904B43C38F5DA2C1BCCE /* Recipe.swift */,
				BFD881A4E73B5A047F08F6EB /* RecipeDetail.swift */,
				2D0D2194CA4CE570714310BB /* RecipeGenerationRequest.swift */,
				BD838F051CCB17441EB36057 /* RecipeGenerationTypes.swift */,
				00C6D31A5B45C429D2C7BC73 /* RecipeUIModel.swift */,
				55ACDE8516C85BC6B4D16CC7 /* RegenerateModeState.swift */,
				416FE6AD741760160A09D36A /* RegenerateRequest.swift */,
				9FD4633A895C41348C22C35D /* RemoteConfiguration.swift */,
				D4867FFC3C03EA927A2A4619 /* SwiftDataModels.swift */,
				AB4EE67748A5F44F137361C2 /* UserPreferences.swift */,
				35CDF50BB12C724D6BBE8CC2 /* ViewState.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		B4B9DBFF954BD5347C879108 /* Features */ = {
			isa = PBXGroup;
			children = (
				1FC412429FB925EF376F80A6 /* 1_ImageCapture */,
				72C95A01F0CC48629DF9E9B1 /* 2_Results */,
				75DE33128F7D036CC2DBC4AA /* 3_Results */,
				777F36B8DA01C8AB0E61EDA1 /* Debug */,
				8AF856DE78DD25E25BA3690A /* Pantry */,
				3B995C1EF162DAD5B2491BF9 /* Profile */,
				40D60551B89A5B09AD08F353 /* RecipeGenerator */,
				E5390B68F1EF030BF301801A /* Recipes */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		BBEFB2E0D465492D3CC1B9F4 /* Application */ = {
			isa = PBXGroup;
			children = (
				9BF94B7ADBCEE860F7B723CC /* api-keys.plist */,
				3C6BAB000A706022E5DE4311 /* App.swift */,
				3C4267156C55BCA053A6D391 /* Assets.xcassets */,
				8AE611CBEB6F9C65CA8F9BF6 /* GoogleService-Info.plist */,
				176398FEADBD4CF189075941 /* Info.plist */,
				70D683F0BF9B783BB0F51EE6 /* IngredientScanner.entitlements */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		C28058C6E36FC3F337B3F925 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				A6E814F10D859D0594F1E3C2 /* APIKeys.swift */,
				F051D9F4E367F48AC03E0B61 /* Haptics.swift */,
				C093F1FD032970F0D124F474 /* ImagePicker.swift */,
				11DBEC63A8F70F25F4C5480C /* MultiImagePicker.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		D463D5725897FDF54E6EABBC = {
			isa = PBXGroup;
			children = (
				BBEFB2E0D465492D3CC1B9F4 /* Application */,
				A1F9EAED5F5762EE342BAE4F /* Coordinator */,
				B4B9DBFF954BD5347C879108 /* Features */,
				A3C06954CFBF169AC85CF7F6 /* Models */,
				E8F39B9E25349F9BC1411826 /* Services */,
				C28058C6E36FC3F337B3F925 /* Utilities */,
				DDD72ED76DA11BC22FFC4895 /* Products */,
			);
			sourceTree = "<group>";
		};
		DDD72ED76DA11BC22FFC4895 /* Products */ = {
			isa = PBXGroup;
			children = (
				D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E5390B68F1EF030BF301801A /* Recipes */ = {
			isa = PBXGroup;
			children = (
				98B8CC92CB9B5D9E94B23193 /* RecipesView.swift */,
				5DDDD7E86CC44475BCCD2D93 /* RecipesViewModel.swift */,
			);
			path = Recipes;
			sourceTree = "<group>";
		};
		E8F39B9E25349F9BC1411826 /* Services */ = {
			isa = PBXGroup;
			children = (
				59F5CB5CD990DF3795FBEA8D /* APIProtocols.swift */,
				2AE13D94EDA6D7C6CF36F861 /* AsyncImagePreparationService.swift */,
				002990977B33054F36A3499D /* AuthenticationService.swift */,
				618BC01DC46E43C192A972EA /* ConcurrencyManager.swift */,
				FB908385AD7D068AB5312E67 /* ExpirationAlertManager.swift */,
				8B0B1D1EECDE31EBE316148E /* ExpirationNotificationManager.swift */,
				DBF14D5276C786D79A3296EA /* FavoritesStore.swift */,
				C0BF0FCC9FBAD1F55D36784E /* FirestoreError.swift */,
				96E57515DB53E2C4895273E7 /* GeminiAPIService.swift */,
				DA27F2449FA133E6C7BD796D /* GoogleVisionAPIService.swift */,
				A6C33700D0F38BFB26D4F150 /* IngredientLibrary.swift */,
				C232D41B35C988DB1ED2C337 /* OptimizedNetworkService.swift */,
				54911ABF20CE85EEE21D36B4 /* PantryOrganizerService.swift */,
				DFC23B81C1836632A9C0C7D1 /* PantryService.swift */,
				BDAD84D6504FD42EA06C4AFD /* PantryStateProvider.swift */,
				2F8D4B506DEF312B02334F4C /* PlanStore.swift */,
				9C4BB6A173DBA2A26848EBEB /* PreparedImage.swift */,
				DD5874C638A51E908AA3C197 /* RecipeDetailCache.swift */,
				CF40FDE398B1A6CE1FD2B0B7 /* RecipeGenerationService.swift */,
				561B59FB1C991C51A3AF05DB /* RecipeGrouper.swift */,
				FE2E2AED4CE768FFB8719125 /* RecipeRequestBuilder.swift */,
				D76E654E89C20F29237B9984 /* RecipeServiceAdapter.swift */,
				EACA497F9E6F77C9E4D26016 /* RemoteConfigurationManager.swift */,
				3CA085D99E48956579051C8A /* ServiceContainer.swift */,
				508254809ABE98C8FD2CD764 /* SummaryGenerator.swift */,
				81535A450F231A7A47014D5C /* SwiftDataStorageService.swift */,
				4BF734808AFF0C664EA566A1 /* TelemetryEventBuilder.swift */,
				67762E49563B93104A5F83EC /* TelemetryService.swift */,
				E45162584291B28B845255C8 /* UserProfileService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		930782F8BD78C73099163318 /* IngredientScanner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 934DAD8BBAF2712F704AEE6A /* Build configuration list for PBXNativeTarget "IngredientScanner" */;
			buildPhases = (
				ADD5E749E813581FAABB57D6 /* Sources */,
				D58938D13C4BC5DD9A99C61C /* Resources */,
				0786AF5BB94AD8A44FA2C5E8 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = IngredientScanner;
			packageProductDependencies = (
				ABE43B4277A5DC9DFB4721A7 /* FirebaseAuth */,
				B4B07A517E72BCF7376D449A /* FirebaseFirestore */,
				D59E35DCDAE975A10BC98623 /* FirebaseAnalytics */,
				C7CA2B5F33DE3455FFB8FD33 /* GoogleSignIn */,
			);
			productName = IngredientScanner;
			productReference = D0E63A4B380015E8E3BF68C8 /* IngredientScanner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2212BE8852EE267CB6C8FE89 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					930782F8BD78C73099163318 = {
						DevelopmentTeam = "";
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 3F5BB50E25893847BAF617DA /* Build configuration list for PBXProject "IngredientScanner" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = D463D5725897FDF54E6EABBC;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				79A7909AF8C1ADE48D12BC9C /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			preferredProjectObjectVersion = 77;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				930782F8BD78C73099163318 /* IngredientScanner */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		D58938D13C4BC5DD9A99C61C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				190112BDFD568D7BD1682DCA /* Assets.xcassets in Resources */,
				929C5362403990D168CD15BC /* GoogleService-Info.plist in Resources */,
				BDBFE1EE64AC1ACE45B2E9E8 /* api-keys.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		ADD5E749E813581FAABB57D6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5ED14FC0EC3F3AA2E63FD535 /* APIKeys.swift in Sources */,
				F5CE6AFD5A60E98F7FA9586E /* APIProtocols.swift in Sources */,
				78C944EA1DE275CA98A3A83A /* AllergiesIntolerancesView.swift in Sources */,
				470B030CFE6F3D8EF8AF9C97 /* App.swift in Sources */,
				88D2F764123B52301A3B70B8 /* AppCoordinator.swift in Sources */,
				728037D12D75D4B900849FE8 /* AsyncImagePreparationService.swift in Sources */,
				350020676BBFA2E592C4CE7A /* AuthenticationService.swift in Sources */,
				1C514D8C68BD8C0DA425F4B0 /* ConcurrencyManager.swift in Sources */,
				A13ABB8AE497C0CF26649E19 /* ConfigurationSummaryCard.swift in Sources */,
				89AFB66F259BE7565A5D1801 /* DebugView.swift in Sources */,
				6C6A417E7F5E0972303323D6 /* DebugViewModel.swift in Sources */,
				5C0A0B8D64D2DE136D8CBE73 /* DietaryRestrictionsView.swift in Sources */,
				AF1C7E872F143AB22E28AFEB /* DisplayError.swift in Sources */,
				56D28E25E361C9BED25A9AE7 /* EnhancedRecipeCard.swift in Sources */,
				AEE1492DE07FCE6AEB73AC6A /* EquipmentChipsView.swift in Sources */,
				076E088D06AEA68BF880E443 /* EquipmentView.swift in Sources */,
				1602CCC1FCD042DA353BFB5D /* ErrorView.swift in Sources */,
				546FD3797670C44238850D6F /* ExpirationAlertManager.swift in Sources */,
				3E55F8A163B930B0C10DF7C3 /* ExpirationNotificationManager.swift in Sources */,
				83EC4916030D1DE92209B3FB /* FamilySizeView.swift in Sources */,
				57C0F9F47D4919B96AB3C9A9 /* FavoritesStore.swift in Sources */,
				9F593E6A75E3601FB6CA8770 /* FirestoreError.swift in Sources */,
				590EF66376A8348CBBBF4BCF /* GeminiAPIService.swift in Sources */,
				82CFCA724ABD5520D70A210B /* GenerateButton.swift in Sources */,
				46D1AA5FBF4641ECC38CAB93 /* GeneratedRecipeDetailView.swift in Sources */,
				E684CC98841467E1D92339FE /* GoogleVisionAPIService.swift in Sources */,
				11C58082195D9D6D63494CFC /* GroupedResultsView.swift in Sources */,
				C93D1E772CC97F0DFA65CD27 /* Haptics.swift in Sources */,
				16947ACBFFE296C4C5608521 /* ImagePicker.swift in Sources */,
				9AC0D19AA92FEA4636B57E8D /* Ingredient.swift in Sources */,
				F34EAA77130AA93558A20E26 /* IngredientLibrary.swift in Sources */,
				456E8CB6D048D357117FDAE1 /* LoadingView.swift in Sources */,
				9980C88FF2EF298D70231142 /* ModeSelector.swift in Sources */,
				3BF985B6E33165CCCAB97988 /* MultiImagePicker.swift in Sources */,
				EFEA08C3450FB79F8941B2F8 /* NotificationsView.swift in Sources */,
				BF4FB6EDD3523DB2EA76C06B /* OptimizedNetworkService.swift in Sources */,
				465AA90B58DBF295DF9729FB /* PantryCategory.swift in Sources */,
				ADD1FCE71785114E258ECA89 /* PantryOrganizerService.swift in Sources */,
				3CEEAA16C0A38AD1F62E30E0 /* PantryService.swift in Sources */,
				4DDFE5D0B3F7B40FEFBA78BD /* PantryStateProvider.swift in Sources */,
				1A3D422E366B31A3F4C92CA7 /* PantryStateWarning.swift in Sources */,
				9634C6A0A790DFCF8DB74308 /* PantryView.swift in Sources */,
				2009917FD1AE77EF5C26151D /* PantryViewModel.swift in Sources */,
				CC1C55DA7952A3D044AC8245 /* PlanStore.swift in Sources */,
				5CDD243A8D50CF3B1D79508C /* PreferencesEditView.swift in Sources */,
				84995C5F1A57FF917F21854C /* PreparedImage.swift in Sources */,
				DC6AC64160026E6BE9CA4292 /* ProcessingScreen.swift in Sources */,
				5FA62A0FB480B7DD1ACFFBFC /* ProfileView.swift in Sources */,
				257DCFADF4F8AA5375CEAAE6 /* QuickResultsView.swift in Sources */,
				286D3AD60207B07E1922ED46 /* Recipe.swift in Sources */,
				D2CB3623DB80D90596DFAFB8 /* RecipeCard.swift in Sources */,
				9AAB43E55D0D950A9774FB0D /* RecipeCardView.swift in Sources */,
				D86249C65F27BEE3D39041E4 /* RecipeDetail.swift in Sources */,
				04F4BC4DFB7E9DCA9E050276 /* RecipeDetailCache.swift in Sources */,
				EEED6D2A397A1A7701CA5D9C /* RecipeGenerationRequest.swift in Sources */,
				EC9C0184D659F23C2A4D1112 /* RecipeGenerationService.swift in Sources */,
				AC5A62C1B1C0B0F554DC12F6 /* RecipeGenerationTypes.swift in Sources */,
				EDB1FCBE871A2FC42E1BB1B0 /* RecipeGeneratorView.swift in Sources */,
				E6D16E11F54A7B3AC607A39C /* RecipeGeneratorViewModel.swift in Sources */,
				10DB2E5C2CC15BC608773B0B /* RecipeGrouper.swift in Sources */,
				8E3182790C46308A760BF69D /* RecipeRequestBuilder.swift in Sources */,
				DEFA83DECCA0F8397BBA36DA /* RecipeServiceAdapter.swift in Sources */,
				2CD4FB43C1F24D0A6C270602 /* RecipeUIModel.swift in Sources */,
				3BBAF884691CE8BCAC30AFE1 /* RecipesView.swift in Sources */,
				F97E1CE239E7C4724DABF792 /* RecipesViewModel.swift in Sources */,
				D2694E9BF7B54C29F8F913BE /* RegenerateModeState.swift in Sources */,
				11F4D8DE14658CEE89A81B24 /* RegenerateRequest.swift in Sources */,
				40FEB17B88C5F3765C569E97 /* RemoteConfiguration.swift in Sources */,
				73076B2B7A47E522AAECBD7F /* RemoteConfigurationManager.swift in Sources */,
				300FF772D541C1E8D839037D /* ResultsView.swift in Sources */,
				4CAC1DE461273018EEC02DA7 /* ResultsViewModel.swift in Sources */,
				F9A41FBACF9CCA4A42169055 /* ServiceContainer.swift in Sources */,
				9D480EEED0BF61466A814CC5 /* SettingsView.swift in Sources */,
				45D2DE7DBB798692C6F82957 /* SignInView.swift in Sources */,
				7A3011B5F41A0480B47E8D1C /* StagingView.swift in Sources */,
				8C6699499ED137FE3BBEDE00 /* StagingViewModel.swift in Sources */,
				9B8AA968C7F1DBC83CB101DD /* StrictExclusionsView.swift in Sources */,
				76672B856A0F24B02726596D /* SummaryGenerator.swift in Sources */,
				3AFFA6126A9555091B68EAC6 /* SwiftDataModels.swift in Sources */,
				74F5ADC7DEBC005347DFF87A /* SwiftDataStorageService.swift in Sources */,
				BE82E83A77935AA118FD1AA6 /* TelemetryEventBuilder.swift in Sources */,
				FF18CF88C55BAAED3384A5A3 /* TelemetryService.swift in Sources */,
				4FD023AD5DA226B136291273 /* ToastView.swift in Sources */,
				A9EE505F6BA66751830BC279 /* UserPreferences.swift in Sources */,
				D79545A53384C3A69227721F /* UserProfileService.swift in Sources */,
				405B7FF8557B6E9D50708FB4 /* ViewState.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A880B49E585BD8E0F7594CBB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = Application/IngredientScanner.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				INFOPLIST_FILE = Application/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuo.ingredientscannertemp;
				PRODUCT_NAME = IngredientScanner;
				SDKROOT = iphoneos;
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B03696EE21174C7DACC19C30 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = Application/IngredientScanner.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				INFOPLIST_FILE = Application/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.kuo.ingredientscannertemp;
				PRODUCT_NAME = IngredientScanner;
				SDKROOT = iphoneos;
				SWIFT_STRICT_CONCURRENCY = complete;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E99B563E6A920425D0D3542F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		F0F865AAC6BB2C1182B5E8D8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3F5BB50E25893847BAF617DA /* Build configuration list for PBXProject "IngredientScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F0F865AAC6BB2C1182B5E8D8 /* Debug */,
				E99B563E6A920425D0D3542F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		934DAD8BBAF2712F704AEE6A /* Build configuration list for PBXNativeTarget "IngredientScanner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B03696EE21174C7DACC19C30 /* Debug */,
				A880B49E585BD8E0F7594CBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		79A7909AF8C1ADE48D12BC9C /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		ABE43B4277A5DC9DFB4721A7 /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		B4B07A517E72BCF7376D449A /* FirebaseFirestore */ = {
			isa = XCSwiftPackageProductDependency;
			package = F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestore;
		};
		C7CA2B5F33DE3455FFB8FD33 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A7909AF8C1ADE48D12BC9C /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		D59E35DCDAE975A10BC98623 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = F03E3D36680729198EB6D5B2 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2212BE8852EE267CB6C8FE89 /* Project object */;
}
