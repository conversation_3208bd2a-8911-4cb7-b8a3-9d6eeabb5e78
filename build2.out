Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -scheme IngredientScanner -destination "platform=iOS Simulator,name=iPhone 16" build

Resolve Package Graph


Resolved source packages:
  gRPC: https://github.com/google/grpc-binary.git @ 1.69.0
  GoogleUtilities: https://github.com/google/GoogleUtilities.git @ 8.1.0
  AppCheck: https://github.com/google/app-check.git @ 11.2.0
  InteropForGoogle: https://github.com/google/interop-ios-for-google-sdks.git @ 101.0.0
  GoogleAdsOnDeviceConversion: https://github.com/googleads/google-ads-on-device-conversion-ios-sdk @ 2.2.1
  abseil: https://github.com/google/abseil-cpp-binary.git @ 1.2024072200.0
  Firebase: https://github.com/firebase/firebase-ios-sdk @ 11.15.0
  GTMSessionFetcher: https://github.com/google/gtm-session-fetcher.git @ 3.5.0
  GoogleDataTransport: https://github.com/google/GoogleDataTransport.git @ 10.1.0
  GoogleSignIn: https://github.com/google/GoogleSignIn-iOS @ 8.0.0
  GoogleAppMeasurement: https://github.com/google/GoogleAppMeasurement.git @ 11.15.0
  leveldb: https://github.com/firebase/leveldb.git @ 1.22.5
  AppAuth: https://github.com/openid/AppAuth-iOS.git @ 1.7.6
  nanopb: https://github.com/firebase/nanopb.git @ 2.30910.0
  SwiftProtobuf: https://github.com/apple/swift-protobuf.git @ 1.30.0
  GTMAppAuth: https://github.com/google/GTMAppAuth.git @ 4.1.1
  Promises: https://github.com/google/promises.git @ 2.4.0

ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (91 targets)
    Target 'IngredientScanner' in project 'IngredientScanner'
        ➜ Explicit dependency on target 'FirebaseAuth' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseFirestore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAnalytics' in project 'Firebase'
        ➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
    Target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
        ➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
        ➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'AppAuthCore' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuthCore' in project 'AppAuth'
    Target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth' (no dependencies)
    Target 'AppCheckCore' in project 'AppCheck'
        ➜ Explicit dependency on target 'AppCheckCore' in project 'AppCheck'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
    Target 'AppCheckCore' in project 'AppCheck'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
    Target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'AppAuthCore' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuthCore' in project 'AppAuth'
    Target 'AppAuth_AppAuthCore' in project 'AppAuth' (no dependencies)
    Target 'AppAuth_AppAuth' in project 'AppAuth' (no dependencies)
    Target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn' (no dependencies)
    Target 'FirebaseAnalytics' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAnalyticsTarget' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAnalyticsWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'FirebaseAnalyticsTarget' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAnalyticsWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'FirebaseAnalyticsWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GoogleAppMeasurementTarget' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'GoogleAdsOnDeviceConversion' in project 'GoogleAdsOnDeviceConversion'
    Target 'GoogleAppMeasurementTarget' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'GoogleAdsOnDeviceConversion' in project 'GoogleAdsOnDeviceConversion'
    Target 'GoogleAdsOnDeviceConversion' in project 'GoogleAdsOnDeviceConversion'
        ➜ Explicit dependency on target 'GoogleAdsOnDeviceConversionTarget' in project 'GoogleAdsOnDeviceConversion'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'GoogleAdsOnDeviceConversionTarget' in project 'GoogleAdsOnDeviceConversion'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
    Target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
    Target 'GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
    Target 'GoogleUtilities_GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities' (no dependencies)
    Target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
    Target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
    Target 'GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
    Target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities' (no dependencies)
    Target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'Promises_FBLPromises' in project 'Promises'
    Target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'Promises_FBLPromises' in project 'Promises'
    Target 'Promises_FBLPromises' in project 'Promises' (no dependencies)
    Target 'Firebase_FirebaseInstallations' in project 'Firebase' (no dependencies)
    Target 'FirebaseFirestore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseFirestoreTarget' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseFirestoreInternalWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSharedSwift' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseFirestore' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'abseil' in project 'abseil'
        ➜ Explicit dependency on target 'gRPC-C++' in project 'gRPC'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'leveldb' in project 'leveldb'
    Target 'FirebaseFirestoreTarget' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseFirestoreInternalWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSharedSwift' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseFirestore' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'abseil' in project 'abseil'
        ➜ Explicit dependency on target 'gRPC-C++' in project 'gRPC'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'leveldb' in project 'leveldb'
    Target 'FirebaseFirestore' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseFirestore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseFirestoreInternalWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSharedSwift' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'abseil' in project 'abseil'
        ➜ Explicit dependency on target 'gRPC-C++' in project 'gRPC'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'leveldb' in project 'leveldb'
    Target 'leveldb' in project 'leveldb'
        ➜ Explicit dependency on target 'leveldb' in project 'leveldb'
        ➜ Explicit dependency on target 'leveldb_leveldb' in project 'leveldb'
    Target 'leveldb' in project 'leveldb'
        ➜ Explicit dependency on target 'leveldb_leveldb' in project 'leveldb'
    Target 'leveldb_leveldb' in project 'leveldb' (no dependencies)
    Target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'nanopb_nanopb' in project 'nanopb'
    Target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'nanopb_nanopb' in project 'nanopb'
    Target 'nanopb_nanopb' in project 'nanopb' (no dependencies)
    Target 'gRPC-C++' in project 'gRPC'
        ➜ Explicit dependency on target 'grpcppWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'gRPC_grpcppWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'grpcWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'opensslWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'abseil' in project 'abseil'
    Target 'grpcppWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'gRPC_grpcppWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'grpcWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'opensslWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'abseil' in project 'abseil'
    Target 'opensslWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'gRPC_opensslWrapper' in project 'gRPC'
    Target 'gRPC_opensslWrapper' in project 'gRPC' (no dependencies)
    Target 'grpcWrapper' in project 'gRPC'
        ➜ Explicit dependency on target 'gRPC_grpcWrapper' in project 'gRPC'
    Target 'gRPC_grpcWrapper' in project 'gRPC' (no dependencies)
    Target 'gRPC_grpcppWrapper' in project 'gRPC' (no dependencies)
    Target 'abseil' in project 'abseil'
        ➜ Explicit dependency on target 'abslWrapper' in project 'abseil'
        ➜ Explicit dependency on target 'abseil_abslWrapper' in project 'abseil'
    Target 'abslWrapper' in project 'abseil'
        ➜ Explicit dependency on target 'abseil_abslWrapper' in project 'abseil'
    Target 'abseil_abslWrapper' in project 'abseil' (no dependencies)
    Target 'Firebase_FirebaseFirestore' in project 'Firebase' (no dependencies)
    Target 'FirebaseSharedSwift' in project 'Firebase' (no dependencies)
    Target 'FirebaseFirestoreInternalWrapper' in project 'Firebase' (no dependencies)
    Target 'FirebaseAuth' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAuth' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseAuth' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAuthInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAuthInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'FirebaseAuth' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseAuth' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAppCheckInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAuthInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAuthInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher' (no dependencies)
    Target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
    Target 'GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
    Target 'GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
    Target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities' (no dependencies)
    Target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
    Target 'GoogleUtilities_GoogleUtilities-Reachability' in project 'GoogleUtilities' (no dependencies)
    Target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities' (no dependencies)
    Target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCoreExtension' in project 'Firebase'
    Target 'Firebase_FirebaseCoreExtension' in project 'Firebase' (no dependencies)
    Target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
    Target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
    Target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
    Target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities' (no dependencies)
    Target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
    Target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
    Target 'third-party-IsAppEncrypted' in project 'GoogleUtilities' (no dependencies)
    Target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities' (no dependencies)
    Target 'Firebase_FirebaseCore' in project 'Firebase' (no dependencies)
    Target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
    Target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities'
    Target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities'
    Target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities' (no dependencies)
    Target 'Firebase_FirebaseCoreInternal' in project 'Firebase' (no dependencies)
    Target 'Firebase' in project 'Firebase' (no dependencies)
    Target 'FirebaseAuthInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
    Target 'RecaptchaInterop' in project 'InteropForGoogle'
        ➜ Explicit dependency on target 'RecaptchaInterop' in project 'InteropForGoogle'
    Target 'RecaptchaInterop' in project 'InteropForGoogle' (no dependencies)
    Target 'FirebaseAuthInterop' in project 'Firebase' (no dependencies)
    Target 'FirebaseAppCheckInterop' in project 'Firebase' (no dependencies)
    Target 'Firebase_FirebaseAuth' in project 'Firebase' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -x objective-c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --print-asset-tag-combinations --output-format xml1 /Users/<USER>/Desktop/ingredient-scanner/Application/Assets.xcassets

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -x c++ -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

Build description signature: 0504400ce15307823011506f65bd4bfe
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/XCBuildData/0504400ce15307823011506f65bd4bfe.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache
    cd /Users/<USER>/Desktop/ingredient-scanner/IngredientScanner.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseAuth.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseAuth.build/empty-Firebase_FirebaseAuth.plist (in target 'Firebase_FirebaseAuth' from project 'Firebase')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseAuth.build/empty-Firebase_FirebaseAuth.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseAuth.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreInternal.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseCoreInternal.build/empty-Firebase_FirebaseCoreInternal.plist (in target 'Firebase_FirebaseCoreInternal' from project 'Firebase')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseCoreInternal.build/empty-Firebase_FirebaseCoreInternal.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreInternal.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-NSData.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-NSData.build/empty-GoogleUtilities_GoogleUtilities-NSData.plist (in target 'GoogleUtilities_GoogleUtilities-NSData' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-NSData.build/empty-GoogleUtilities_GoogleUtilities-NSData.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-NSData.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCore.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseCore.build/empty-Firebase_FirebaseCore.plist (in target 'Firebase_FirebaseCore' from project 'Firebase')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseCore.build/empty-Firebase_FirebaseCore.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCore.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Environment.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Environment.build/empty-GoogleUtilities_GoogleUtilities-Environment.plist (in target 'GoogleUtilities_GoogleUtilities-Environment' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Environment.build/empty-GoogleUtilities_GoogleUtilities-Environment.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Environment.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Logger.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Logger.build/empty-GoogleUtilities_GoogleUtilities-Logger.plist (in target 'GoogleUtilities_GoogleUtilities-Logger' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Logger.build/empty-GoogleUtilities_GoogleUtilities-Logger.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Logger.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreExtension.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseCoreExtension.build/empty-Firebase_FirebaseCoreExtension.plist (in target 'Firebase_FirebaseCoreExtension' from project 'Firebase')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseCoreExtension.build/empty-Firebase_FirebaseCoreExtension.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreExtension.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.build/empty-GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.plist (in target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.build/empty-GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Reachability.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Reachability.build/empty-GoogleUtilities_GoogleUtilities-Reachability.plist (in target 'GoogleUtilities_GoogleUtilities-Reachability' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Reachability.build/empty-GoogleUtilities_GoogleUtilities-Reachability.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Reachability.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Network.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Network.build/empty-GoogleUtilities_GoogleUtilities-Network.plist (in target 'GoogleUtilities_GoogleUtilities-Network' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Network.build/empty-GoogleUtilities_GoogleUtilities-Network.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Network.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMSessionFetcher_GTMSessionFetcherCore.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GTMSessionFetcher.build/Debug-iphonesimulator/GTMSessionFetcher_GTMSessionFetcherCore.build/empty-GTMSessionFetcher_GTMSessionFetcherCore.plist (in target 'GTMSessionFetcher_GTMSessionFetcherCore' from project 'GTMSessionFetcher')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/gtm-session-fetcher
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GTMSessionFetcher.build/Debug-iphonesimulator/GTMSessionFetcher_GTMSessionFetcherCore.build/empty-GTMSessionFetcher_GTMSessionFetcherCore.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMSessionFetcher_GTMSessionFetcherCore.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseFirestore.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseFirestore.build/empty-Firebase_FirebaseFirestore.plist (in target 'Firebase_FirebaseFirestore' from project 'Firebase')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseFirestore.build/empty-Firebase_FirebaseFirestore.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseFirestore.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/abseil_abslWrapper.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/abseil.build/Debug-iphonesimulator/abseil_abslWrapper.build/empty-abseil_abslWrapper.plist (in target 'abseil_abslWrapper' from project 'abseil')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/abseil-cpp-binary
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/abseil.build/Debug-iphonesimulator/abseil_abslWrapper.build/empty-abseil_abslWrapper.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/abseil_abslWrapper.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcWrapper.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/gRPC.build/Debug-iphonesimulator/gRPC_grpcWrapper.build/empty-gRPC_grpcWrapper.plist (in target 'gRPC_grpcWrapper' from project 'gRPC')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/gRPC.build/Debug-iphonesimulator/gRPC_grpcWrapper.build/empty-gRPC_grpcWrapper.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcWrapper.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_opensslWrapper.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/gRPC.build/Debug-iphonesimulator/gRPC_opensslWrapper.build/empty-gRPC_opensslWrapper.plist (in target 'gRPC_opensslWrapper' from project 'gRPC')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/gRPC.build/Debug-iphonesimulator/gRPC_opensslWrapper.build/empty-gRPC_opensslWrapper.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_opensslWrapper.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcppWrapper.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/gRPC.build/Debug-iphonesimulator/gRPC_grpcppWrapper.build/empty-gRPC_grpcppWrapper.plist (in target 'gRPC_grpcppWrapper' from project 'gRPC')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/gRPC.build/Debug-iphonesimulator/gRPC_grpcppWrapper.build/empty-gRPC_grpcppWrapper.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcppWrapper.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/nanopb_nanopb.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/nanopb.build/Debug-iphonesimulator/nanopb_nanopb.build/empty-nanopb_nanopb.plist (in target 'nanopb_nanopb' from project 'nanopb')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/nanopb
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/nanopb.build/Debug-iphonesimulator/nanopb_nanopb.build/empty-nanopb_nanopb.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/nanopb_nanopb.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/leveldb_leveldb.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/leveldb.build/Debug-iphonesimulator/leveldb_leveldb.build/empty-leveldb_leveldb.plist (in target 'leveldb_leveldb' from project 'leveldb')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/leveldb
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/leveldb.build/Debug-iphonesimulator/leveldb_leveldb.build/empty-leveldb_leveldb.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/leveldb_leveldb.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMAppAuth_GTMAppAuth.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GTMAppAuth.build/Debug-iphonesimulator/GTMAppAuth_GTMAppAuth.build/empty-GTMAppAuth_GTMAppAuth.plist (in target 'GTMAppAuth_GTMAppAuth' from project 'GTMAppAuth')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GTMAppAuth
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GTMAppAuth.build/Debug-iphonesimulator/GTMAppAuth_GTMAppAuth.build/empty-GTMAppAuth_GTMAppAuth.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMAppAuth_GTMAppAuth.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuthCore.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/AppAuth.build/Debug-iphonesimulator/AppAuth_AppAuthCore.build/empty-AppAuth_AppAuthCore.plist (in target 'AppAuth_AppAuthCore' from project 'AppAuth')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/AppAuth.build/Debug-iphonesimulator/AppAuth_AppAuthCore.build/empty-AppAuth_AppAuthCore.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuthCore.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleSignIn_GoogleSignIn.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleSignIn.build/Debug-iphonesimulator/GoogleSignIn_GoogleSignIn.build/empty-GoogleSignIn_GoogleSignIn.plist (in target 'GoogleSignIn_GoogleSignIn' from project 'GoogleSignIn')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleSignIn-iOS
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleSignIn.build/Debug-iphonesimulator/GoogleSignIn_GoogleSignIn.build/empty-GoogleSignIn_GoogleSignIn.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleSignIn_GoogleSignIn.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseInstallations.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseInstallations.build/empty-Firebase_FirebaseInstallations.plist (in target 'Firebase_FirebaseInstallations' from project 'Firebase')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/Firebase_FirebaseInstallations.build/empty-Firebase_FirebaseInstallations.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseInstallations.bundle/Info.plist

ProcessProductPackaging /Users/<USER>/Desktop/ingredient-scanner/Application/IngredientScanner.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    
    Entitlements:
    
    {
    "application-identifier" = "WJM5MTA2FG.com.kuo.ingredientscannertemp";
}
    
    builtin-productPackagingUtility /Users/<USER>/Desktop/ingredient-scanner/Application/IngredientScanner.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent

ProcessProductPackaging /Users/<USER>/Desktop/ingredient-scanner/Application/IngredientScanner.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app.xcent (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    
    Entitlements:
    
    {
}
    
    builtin-productPackagingUtility /Users/<USER>/Desktop/ingredient-scanner/Application/IngredientScanner.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app.xcent

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent.der (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent.der --raw

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app.xcent.der (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app.xcent.der --raw

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Promises_FBLPromises.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Promises.build/Debug-iphonesimulator/Promises_FBLPromises.build/empty-Promises_FBLPromises.plist (in target 'Promises_FBLPromises' from project 'Promises')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/promises
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Promises.build/Debug-iphonesimulator/Promises_FBLPromises.build/empty-Promises_FBLPromises.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Promises_FBLPromises.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuth.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/AppAuth.build/Debug-iphonesimulator/AppAuth_AppAuth.build/empty-AppAuth_AppAuth.plist (in target 'AppAuth_AppAuth' from project 'AppAuth')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/AppAuth.build/Debug-iphonesimulator/AppAuth_AppAuth.build/empty-AppAuth_AppAuth.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuth.bundle/Info.plist

SwiftDriver IngredientScanner normal arm64 com.apple.xcode.tools.swift.compiler (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name IngredientScanner -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/RecaptchaInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAuthInternal.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAuthInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/abslWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/opensslWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/grpcWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/grpcppWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/leveldb.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCoreExtension.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAppCheckInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseFirestoreInternalWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseFirestoreTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleAdsOnDeviceConversionTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleAppMeasurementTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-AppDelegateSwizzler.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-MethodSwizzler.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Reachability.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-NSData.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Network.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/nanopb.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAnalyticsWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseInstallations.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAnalyticsTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuth.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Logger.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Environment.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/third-party-IsAppEncrypted.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-UserDefaults.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppCheckCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuthCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GTMSessionFetcherCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleSignIn.modulemap -enable-upcoming-feature StrictConcurrency -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -target arm64-apple-ios17.0-simulator -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner-4c169a376e042aeee1401a9959902535-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/interop-ios-for-google-sdks/RecaptchaEnterprise/RecaptchaInterop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Interop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/abseil-cpp-binary/absl-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/openssl-grpc-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/grpc-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/grpcpp-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/leveldb/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Extension -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAppCheck/Interop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseFirestoreInternal -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/SwiftPM-PlatformExclude/FirebaseFirestoreWrap/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/google-ads-on-device-conversion-ios-sdk/GoogleAdsOnDeviceConversionWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleAppMeasurement/GoogleAppMeasurementWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/AppDelegateSwizzler/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/MethodSwizzler/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Reachability/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/NSData+zlib/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Network/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/nanopb/spm_headers -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAnalyticsWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseInstallations/Source/Library/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/CoreOnly/Sources -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/SwiftPM-PlatformExclude/FirebaseAnalyticsWrap/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/promises/Sources/FBLPromises/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Logger/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Environment/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/third_party/IsAppEncrypted/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/UserDefaults/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/app-check/AppCheckCore/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuthCore -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/gtm-session-fetcher/Sources/Core/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleSignIn-iOS/GoogleSignIn/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner-Swift.h -working-directory /Users/<USER>/Desktop/ingredient-scanner -experimental-emit-module-separately -disable-cmo

CompileAssetCatalogVariant thinned /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app /Users/<USER>/Desktop/ingredient-scanner/Application/Assets.xcassets (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    /Applications/Xcode.app/Contents/Developer/usr/bin/actool /Users/<USER>/Desktop/ingredient-scanner/Application/Assets.xcassets --compile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_output/thinned --output-format human-readable-text --notices --warnings --export-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_dependencies_thinned --output-partial-info-plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_generated_info.plist_thinned --app-icon AppIcon --accent-color AccentColor --compress-pngs --enable-on-demand-resources YES --filter-for-thinning-device-configuration iPhone17,3 --filter-for-device-os-version 18.5 --development-region en --target-device iphone --target-device ipad --minimum-deployment-target 17.0 --platform iphonesimulator
/* com.apple.actool.compilation-results */
/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_generated_info.plist_thinned
/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_output/thinned/Assets.car


ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-UserDefaults.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-UserDefaults.build/empty-GoogleUtilities_GoogleUtilities-UserDefaults.plist (in target 'GoogleUtilities_GoogleUtilities-UserDefaults' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-UserDefaults.build/empty-GoogleUtilities_GoogleUtilities-UserDefaults.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-UserDefaults.bundle/Info.plist

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-MethodSwizzler.build/empty-GoogleUtilities_GoogleUtilities-MethodSwizzler.plist (in target 'GoogleUtilities_GoogleUtilities-MethodSwizzler' from project 'GoogleUtilities')
    cd /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GoogleUtilities.build/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-MethodSwizzler.build/empty-GoogleUtilities_GoogleUtilities-MethodSwizzler.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/Info.plist

SwiftCompile normal arm64 Compiling\ RecipeGeneratorView.swift /Users/<USER>/Desktop/ingredient-scanner/Features/RecipeGenerator/RecipeGeneratorView.swift (in target 'IngredientScanner' from project 'IngredientScanner')

SwiftCompile normal arm64 /Users/<USER>/Desktop/ingredient-scanner/Features/RecipeGenerator/RecipeGeneratorView.swift (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    

SwiftCompile normal arm64 Compiling\ ModeSelector.swift /Users/<USER>/Desktop/ingredient-scanner/Features/RecipeGenerator/Components/ModeSelector.swift (in target 'IngredientScanner' from project 'IngredientScanner')

SwiftCompile normal arm64 /Users/<USER>/Desktop/ingredient-scanner/Features/RecipeGenerator/Components/ModeSelector.swift (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    

SwiftEmitModule normal arm64 Emitting\ module\ for\ IngredientScanner (in target 'IngredientScanner' from project 'IngredientScanner')

EmitSwiftModule normal arm64 (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    

SwiftCompile normal arm64 Compiling\ RecipeGeneratorViewModel.swift /Users/<USER>/Desktop/ingredient-scanner/Features/RecipeGenerator/RecipeGeneratorViewModel.swift (in target 'IngredientScanner' from project 'IngredientScanner')

SwiftCompile normal arm64 /Users/<USER>/Desktop/ingredient-scanner/Features/RecipeGenerator/RecipeGeneratorViewModel.swift (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    

SwiftDriverJobDiscovery normal arm64 Compiling ModeSelector.swift (in target 'IngredientScanner' from project 'IngredientScanner')

SwiftDriverJobDiscovery normal arm64 Compiling RecipeGeneratorViewModel.swift (in target 'IngredientScanner' from project 'IngredientScanner')

SwiftDriverJobDiscovery normal arm64 Compiling RecipeGeneratorView.swift (in target 'IngredientScanner' from project 'IngredientScanner')

SwiftDriver\ Compilation IngredientScanner normal arm64 com.apple.xcode.tools.swift.compiler (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-Swift-Compilation -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name IngredientScanner -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/RecaptchaInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAuthInternal.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAuthInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/abslWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/opensslWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/grpcWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/grpcppWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/leveldb.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCoreExtension.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAppCheckInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseFirestoreInternalWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseFirestoreTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleAdsOnDeviceConversionTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleAppMeasurementTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-AppDelegateSwizzler.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-MethodSwizzler.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Reachability.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-NSData.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Network.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/nanopb.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAnalyticsWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseInstallations.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAnalyticsTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuth.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Logger.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Environment.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/third-party-IsAppEncrypted.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-UserDefaults.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppCheckCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuthCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GTMSessionFetcherCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleSignIn.modulemap -enable-upcoming-feature StrictConcurrency -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -target arm64-apple-ios17.0-simulator -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner-4c169a376e042aeee1401a9959902535-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/interop-ios-for-google-sdks/RecaptchaEnterprise/RecaptchaInterop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Interop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/abseil-cpp-binary/absl-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/openssl-grpc-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/grpc-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/grpcpp-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/leveldb/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Extension -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAppCheck/Interop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseFirestoreInternal -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/SwiftPM-PlatformExclude/FirebaseFirestoreWrap/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/google-ads-on-device-conversion-ios-sdk/GoogleAdsOnDeviceConversionWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleAppMeasurement/GoogleAppMeasurementWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/AppDelegateSwizzler/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/MethodSwizzler/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Reachability/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/NSData+zlib/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Network/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/nanopb/spm_headers -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAnalyticsWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseInstallations/Source/Library/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/CoreOnly/Sources -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/SwiftPM-PlatformExclude/FirebaseAnalyticsWrap/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/promises/Sources/FBLPromises/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Logger/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Environment/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/third_party/IsAppEncrypted/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/UserDefaults/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/app-check/AppCheckCore/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuthCore -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/gtm-session-fetcher/Sources/Core/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleSignIn-iOS/GoogleSignIn/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner-Swift.h -working-directory /Users/<USER>/Desktop/ingredient-scanner -experimental-emit-module-separately -disable-cmo

SwiftDriverJobDiscovery normal arm64 Emitting module for IngredientScanner (in target 'IngredientScanner' from project 'IngredientScanner')

SwiftDriver\ Compilation\ Requirements IngredientScanner normal arm64 com.apple.xcode.tools.swift.compiler (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-Swift-Compilation-Requirements -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name IngredientScanner -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.SwiftFileList -DDEBUG -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/RecaptchaInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAuthInternal.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAuthInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/abslWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/opensslWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/grpcWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/grpcppWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/leveldb.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCoreExtension.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAppCheckInterop.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseFirestoreInternalWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseFirestoreTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleAdsOnDeviceConversionTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleAppMeasurementTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-AppDelegateSwizzler.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-MethodSwizzler.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Reachability.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-NSData.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Network.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/nanopb.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAnalyticsWrapper.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseInstallations.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/FirebaseAnalyticsTarget.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuth.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Logger.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-Environment.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/third-party-IsAppEncrypted.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleUtilities-UserDefaults.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppCheckCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/AppAuthCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GTMSessionFetcherCore.modulemap -Xcc -fmodule-map-file\=/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GeneratedModuleMaps-iphonesimulator/GoogleSignIn.modulemap -enable-upcoming-feature StrictConcurrency -enable-bare-slash-regex -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -target arm64-apple-ios17.0-simulator -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Index.noindex/DataStore -swift-version 5 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -c -j8 -enable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner-4c169a376e042aeee1401a9959902535-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/interop-ios-for-google-sdks/RecaptchaEnterprise/RecaptchaInterop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAuth/Interop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/abseil-cpp-binary/absl-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/openssl-grpc-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/grpc-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/grpc-binary/grpcpp-Wrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/leveldb/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Extension -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAppCheck/Interop/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseFirestoreInternal -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/SwiftPM-PlatformExclude/FirebaseFirestoreWrap/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/google-ads-on-device-conversion-ios-sdk/GoogleAdsOnDeviceConversionWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleAppMeasurement/GoogleAppMeasurementWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/AppDelegateSwizzler/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/MethodSwizzler/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Reachability/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/NSData+zlib/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Network/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/nanopb/spm_headers -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseAnalyticsWrapper/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseInstallations/Source/Library/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/FirebaseCore/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/CoreOnly/Sources -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/firebase-ios-sdk/SwiftPM-PlatformExclude/FirebaseAnalyticsWrap/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuth -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/promises/Sources/FBLPromises/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Logger/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/Environment/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/third_party/IsAppEncrypted/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleUtilities/GoogleUtilities/UserDefaults/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/app-check/AppCheckCore/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/AppAuth-iOS/Sources/AppAuthCore -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/gtm-session-fetcher/Sources/Core/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/SourcePackages/checkouts/GoogleSignIn-iOS/GoogleSignIn/Sources/Public -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner-Swift.h -working-directory /Users/<USER>/Desktop/ingredient-scanner -experimental-emit-module-separately -disable-cmo

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner.debug.dylib normal (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target arm64-apple-ios17.0-simulator -dynamiclib -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -L/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -filelist /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.LinkFileList -install_name @rpath/IngredientScanner.debug.dylib -Xlinker -rpath -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -Xlinker -rpath -Xlinker @executable_path/Frameworks -dead_strip -Xlinker -object_path_lto -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner_lto.o -rdynamic -Xlinker -no_deduplicate -Xlinker -objc_abi_version -Xlinker 2 -Xlinker -dependency_info -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner_dependency_info.dat -fobjc-link-runtime -L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator -L/usr/lib/swift -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftmodule -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -lc++ -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -Wl,-no_warn_duplicate_libraries -framework CoreGraphics -framework CoreText -framework Foundation -framework LocalAuthentication -framework Security -framework UIKit -framework Security -framework DeviceCheck -framework UIKit -framework Security -lsqlite3 -lc++ -lz -framework StoreKit -lz -framework StoreKit -lsqlite3 -lc++ -lz -lc++ -lc++ -framework SystemConfiguration -framework UIKit -framework Security -framework SafariServices -Xlinker -alias -Xlinker _main -Xlinker ___debug_main_executable_dylib_entry_point -framework FirebaseFirestoreInternal -framework absl -framework grpcpp -framework grpc -framework openssl_grpc -framework FirebaseAnalytics -framework GoogleAppMeasurementIdentitySupport -framework GoogleAppMeasurement -framework GoogleAdsOnDeviceConversion -Xlinker -no_adhoc_codesign -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner.debug.dylib -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseAuth.build/Objects-normal/arm64/FirebaseAuth.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseCoreInternal.build/Objects-normal/arm64/FirebaseCoreInternal.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseSharedSwift.build/Objects-normal/arm64/FirebaseSharedSwift.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/Firebase.build/Debug-iphonesimulator/FirebaseFirestore.build/Objects-normal/arm64/FirebaseFirestore.swiftmodule -Xlinker -add_ast_path -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/GTMAppAuth.build/Debug-iphonesimulator/GTMAppAuth.build/Objects-normal/arm64/GTMAppAuth.swiftmodule

ConstructStubExecutorLinkFileList /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-ExecutorLinkFileList-normal-arm64.txt (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    construct-stub-executor-link-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner.debug.dylib /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a --output /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-ExecutorLinkFileList-normal-arm64.txt
note: Using stub executor library with Swift entry point. (in target 'IngredientScanner' from project 'IngredientScanner')

Ld /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner normal (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Xlinker -reproducible -target arm64-apple-ios17.0-simulator -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -O0 -L/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -F/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator -Xlinker -rpath -Xlinker @executable_path -Xlinker -rpath -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/PackageFrameworks -Xlinker -rpath -Xlinker @executable_path/Frameworks -rdynamic -Xlinker -no_deduplicate -Xlinker -objc_abi_version -Xlinker 2 -e ___debug_blank_executor_main -Xlinker -sectcreate -Xlinker __TEXT -Xlinker __debug_dylib -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-DebugDylibPath-normal-arm64.txt -Xlinker -sectcreate -Xlinker __TEXT -Xlinker __debug_instlnm -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-DebugDylibInstallName-normal-arm64.txt -Xlinker -filelist -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner-ExecutorLinkFileList-normal-arm64.txt -Xlinker -sectcreate -Xlinker __TEXT -Xlinker __entitlements -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent -Xlinker -sectcreate -Xlinker __TEXT -Xlinker __ents_der -Xlinker /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app-Simulated.xcent.der /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner.debug.dylib -Xlinker -no_adhoc_codesign -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftmodule (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftmodule /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.swiftmodule/arm64-apple-ios-simulator.swiftmodule

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftsourceinfo (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.swiftsourceinfo /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.abi.json (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks -rename /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.abi.json /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.swiftmodule/arm64-apple-ios-simulator.abi.json

LinkAssetCatalog /Users/<USER>/Desktop/ingredient-scanner/Application/Assets.xcassets (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-linkAssetCatalog --thinned /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_output/thinned --thinned-dependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_dependencies_thinned --thinned-info-plist-content /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_generated_info.plist_thinned --unthinned /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_output/unthinned --unthinned-dependencies /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_dependencies_unthinned --unthinned-info-plist-content /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_generated_info.plist_unthinned --output /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app --plist-output /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_generated_info.plist
note: Emplaced /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Assets.car (in target 'IngredientScanner' from project 'IngredientScanner')

ExtractAppIntentsMetadata (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor --toolchain-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --module-name IngredientScanner --sdk-root /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk --xcode-version 16F6 --platform-family iOS --deployment-target 17.0 --bundle-identifier com.kuo.ingredientscannertemp --output /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app --target-triple arm64-apple-ios17.0-simulator --binary-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner --dependency-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner_dependency_info.dat --stringsdata-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata --source-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.SwiftFileList --metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.DependencyMetadataFileList --static-metadata-file-list /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.DependencyStaticMetadataFileList --swift-const-vals-list /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/Objects-normal/arm64/IngredientScanner.SwiftConstValuesFileList --compile-time-extraction --deployment-aware-processing --validate-assistant-intents --no-app-shortcuts-localization
2025-08-30 16:59:17.853 appintentsmetadataprocessor[24081:198975] Starting appintentsmetadataprocessor export
2025-08-30 16:59:17.855 appintentsmetadataprocessor[24081:198975] warning: Metadata extraction skipped. No AppIntents.framework dependency found.

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/gRPC_grpcppWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcppWrapper.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcppWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-UserDefaults.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-UserDefaults.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-UserDefaults.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/nanopb_nanopb.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/nanopb_nanopb.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/nanopb_nanopb.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/leveldb_leveldb.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/leveldb_leveldb.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/leveldb_leveldb.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/gRPC_opensslWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_opensslWrapper.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_opensslWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/gRPC_grpcWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcWrapper.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/gRPC_grpcWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/abseil_abslWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/abseil_abslWrapper.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/abseil_abslWrapper.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Promises_FBLPromises.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Promises_FBLPromises.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Promises_FBLPromises.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Reachability.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Reachability.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Reachability.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Network.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Network.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Network.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-NSData.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-NSData.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-NSData.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Environment.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Environment.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Environment.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Logger.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Logger.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-Logger.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleSignIn_GoogleSignIn.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleSignIn_GoogleSignIn.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GoogleSignIn_GoogleSignIn.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GTMSessionFetcher_GTMSessionFetcherCore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMSessionFetcher_GTMSessionFetcherCore.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMSessionFetcher_GTMSessionFetcherCore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GTMAppAuth_GTMAppAuth.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMAppAuth_GTMAppAuth.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/GTMAppAuth_GTMAppAuth.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseInstallations.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseInstallations.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseInstallations.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseFirestore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseFirestore.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseFirestore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseCoreInternal.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreInternal.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreInternal.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseCoreExtension.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreExtension.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCoreExtension.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseCore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCore.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseCore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseAuth.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseAuth.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/Firebase_FirebaseAuth.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/AppAuth_AppAuthCore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuthCore.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuthCore.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Copy /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/AppAuth_AppAuth.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuth.bundle (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-copy -exclude .DS_Store -exclude CVS -exclude .svn -exclude .git -exclude .hg -resolve-src-symlinks /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/AppAuth_AppAuth.bundle /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Info.plist /Users/<USER>/Desktop/ingredient-scanner/Application/Info.plist (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-infoPlistUtility /Users/<USER>/Desktop/ingredient-scanner/Application/Info.plist -producttype com.apple.product-type.application -genpkginfo /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/PkgInfo -expandbuildsettings -format binary -platform iphonesimulator -additionalcontentfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/assetcatalog_generated_info.plist -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/AppAuth_AppAuth.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/AppAuth_AppAuthCore.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseAuth.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseCore.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseCoreExtension.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseCoreInternal.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseFirestore.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Firebase_FirebaseInstallations.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/FirebaseAnalytics.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/FirebaseFirestoreInternal.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/GoogleAdsOnDeviceConversion.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/GoogleAppMeasurement.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/GoogleAppMeasurementIdentitySupport.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/absl.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/grpc.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/grpcpp.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks/openssl_grpc.framework -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GTMAppAuth_GTMAppAuth.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GTMSessionFetcher_GTMSessionFetcherCore.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleSignIn_GoogleSignIn.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Environment.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Logger.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-NSData.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Network.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-Reachability.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/GoogleUtilities_GoogleUtilities-UserDefaults.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Promises_FBLPromises.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/abseil_abslWrapper.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/gRPC_grpcWrapper.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/gRPC_grpcppWrapper.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/gRPC_opensslWrapper.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/leveldb_leveldb.bundle -scanforprivacyfile /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/nanopb_nanopb.bundle -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Info.plist

CopySwiftLibs /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-swiftStdLibTool --copy --verbose --sign - --scan-executable /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner.debug.dylib --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/PlugIns --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/SystemExtensions --scan-folder /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Extensions --platform iphonesimulator --toolchain /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain --destination /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/Frameworks --strip-bitcode --strip-bitcode-tool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/bitcode_strip --emit-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/SwiftStdLibToolInputDependencies.dep --filter-for-swift-os

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner.debug.dylib (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    
    Signing Identity:     "Sign to Run Locally"
    
    /usr/bin/codesign --force --sign - --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/IngredientScanner.debug.dylib

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/__preview.dylib (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    
    Signing Identity:     "Sign to Run Locally"
    
    /usr/bin/codesign --force --sign - --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/__preview.dylib
/Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app/__preview.dylib: replacing existing signature

CodeSign /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    
    Signing Identity:     "Sign to Run Locally"
    
    /usr/bin/codesign --force --sign - --entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Intermediates.noindex/IngredientScanner.build/Debug-iphonesimulator/IngredientScanner.build/IngredientScanner.app.xcent --timestamp\=none --generate-entitlement-der /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app

Validate /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app (in target 'IngredientScanner' from project 'IngredientScanner')
    cd /Users/<USER>/Desktop/ingredient-scanner
    builtin-validationUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IngredientScanner-gbgaaineoyokmmewknjiiuvxleej/Build/Products/Debug-iphonesimulator/IngredientScanner.app -infoplist-subpath Info.plist

** BUILD SUCCEEDED **

