Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -scheme IngredientScanner -destination "platform=iOS Simulator,name=iPhone 15" build

Resolve Package Graph


Resolved source packages:
  SwiftProtobuf: https://github.com/apple/swift-protobuf.git @ 1.30.0
  GoogleDataTransport: https://github.com/google/GoogleDataTransport.git @ 10.1.0
  GoogleAppMeasurement: https://github.com/google/GoogleAppMeasurement.git @ 11.15.0
  leveldb: https://github.com/firebase/leveldb.git @ 1.22.5
  AppCheck: https://github.com/google/app-check.git @ 11.2.0
  abseil: https://github.com/google/abseil-cpp-binary.git @ 1.2024072200.0
  Promises: https://github.com/google/promises.git @ 2.4.0
  GTMAppAuth: https://github.com/google/GTMAppAuth.git @ 4.1.1
  GTMSessionFetcher: https://github.com/google/gtm-session-fetcher.git @ 3.5.0
  InteropForGoogle: https://github.com/google/interop-ios-for-google-sdks.git @ 101.0.0
  GoogleSignIn: https://github.com/google/GoogleSignIn-iOS @ 8.0.0
  AppAuth: https://github.com/openid/AppAuth-iOS.git @ 1.7.6
  Firebase: https://github.com/firebase/firebase-ios-sdk @ 11.15.0
  gRPC: https://github.com/google/grpc-binary.git @ 1.69.0
  GoogleUtilities: https://github.com/google/GoogleUtilities.git @ 8.1.0
  GoogleAdsOnDeviceConversion: https://github.com/googleads/google-ads-on-device-conversion-ios-sdk @ 2.2.1
  nanopb: https://github.com/firebase/nanopb.git @ 2.30910.0

