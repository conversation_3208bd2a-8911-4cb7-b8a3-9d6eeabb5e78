import SwiftUI

struct RecipesView: View {
    @State private var viewModel = RecipesViewModel()
    @Environment(NavigationCoordinator.self) var coordinator: NavigationCoordinator
    @Environment(RegenerateModeState.self) var regenState: RegenerateModeState
    @Environment(AuthenticationService.self) var authService: AuthenticationService
    @Environment(PantryService.self) var pantryService: PantryService

    @State private var isRunningBatch = false

    var body: some View {
        List {
            // Section 1: Quick Result
            if let quick = viewModel.lastQuick, !quick.recipes.isEmpty {
                Section("Quick Result") {
                    ForEach(quick.recipes, id: \.id) { item in
                        NavigationLink(destination: GeneratedRecipeDetailView(recipeUIModel: item)) {
                            HStack {
                                VStack(alignment: .leading) {
                                    Text(item.title).font(.headline)
                                    if let sub = item.subtitle { Text(sub).font(.subheadline).foregroundColor(.secondary) }
                                }
                                Spacer()
                                Image(systemName: "chevron.right").foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }

            // Section 2: Meal Prep Plan
            if let plan = viewModel.lastMealPrep?.plan {
                Section {
                    ForEach(Array(plan.days.enumerated()), id: \.offset) { idx, day in
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text(formattedDate(day.date, index: idx)).font(.headline)
                                Spacer()
                                if !regenState.isSelecting {
                                    Button("Edit/Regenerate") {
                                        withAnimation { regenState.beginSelection() }
                                    }
                                    .font(.subheadline)
                                }
                            }
                            ForEach(day.meals) { slot in
                                if regenState.isSelecting {
                                    Button(action: {
                                        regenState.toggleSelection(slot.slotId)
                                    }) {
                                        HStack {
                                            let status = regenState.slotStatuses[slot.slotId] ?? .idle
                                            if status == .regenerating {
                                                ProgressView().progressViewStyle(.circular)
                                            } else {
                                                Image(systemName: regenState.selectedSlotIds.contains(slot.slotId) ? "checkmark.circle.fill" : "circle")
                                                    .foregroundColor(regenState.selectedSlotIds.contains(slot.slotId) ? .blue : .secondary)
                                            }
                                            Text("\(slot.mealType.displayName): \(slot.recipe.title)")
                                            Spacer()
                                            if regenState.slotStatuses[slot.slotId] == .replaced {
                                                Text("Replaced").font(.caption).foregroundStyle(.green)
                                            } else if regenState.slotStatuses[slot.slotId] == .notReplaced {
                                                Text("Not replaced").font(.caption).foregroundStyle(.red)
                                            }
                                            Image(systemName: "chevron.right").foregroundColor(.secondary)
                                        }
                                    }
                                    .buttonStyle(.plain)
                                } else {
                                    NavigationLink(destination: GeneratedRecipeDetailView(recipeUIModel: slot.recipe)) {
                                        HStack {
                                            Text("\(slot.mealType.displayName): \(slot.recipe.title)")
                                            Spacer()
                                            if regenState.slotStatuses[slot.slotId] == .replaced {
                                                Text("Replaced").font(.caption).foregroundStyle(.green)
                                            } else if regenState.slotStatuses[slot.slotId] == .notReplaced {
                                                Text("Not replaced").font(.caption).foregroundStyle(.red)
                                            }
                                            Image(systemName: "chevron.right").foregroundColor(.secondary)
                                        }
                                    }
                                }
                            }
                        }
                        .listRowInsets(EdgeInsets())
                    }
                } header: {
                    Text("Meal Prep")
                }
            }

            // Section 3: Favorites
            Section("Favorites") {
                if viewModel.favoriteIds.isEmpty {
                    Text("No favorites yet").foregroundStyle(.secondary)
                } else {
                    ForEach(viewModel.favoriteIds, id: \.self) { id in
                        Text(id)
                    }
                }
            }
        }
        .navigationTitle("Recipes")
        .navigationBarTitleDisplayMode(.large)
        .onAppear {
            viewModel.reload()
            // If coming from detail with a pending title, preselect matching slot and ensure selection mode is active
            if let pending = regenState.pendingPreselectTitle, let plan = viewModel.lastMealPrep?.plan {
                if !regenState.isSelecting { regenState.beginSelection() }
                outer: for day in plan.days {
                    for slot in day.meals {
                        if slot.recipe.title == pending {
                            regenState.toggleSelection(slot.slotId)
                            break outer
                        }
                    }
                }
            }
        }
        .safeAreaInset(edge: .bottom) {
            if regenState.isSelecting {
                HStack {
                    Button("Cancel") {
                        withAnimation { regenState.endSelection() }
                    }
                    .buttonStyle(.bordered)
                    Spacer()
                    Button(action: { Task { await runRegeneration() } }) {
                        if isRunningBatch {
                            ProgressView()
                        } else {
                            Text("Regenerate Selected (\(regenState.selectedSlotIds.count))")
                        }
                    }
                    .disabled(isRunningBatch || regenState.selectedSlotIds.isEmpty)
                    .buttonStyle(.borderedProminent)
                }
                .padding(.horizontal)
                .padding(.top, 8)
                .background(.ultraThinMaterial)
            }
        }
    }

    // MARK: - Batch Regenerate
    private func runRegeneration() async {
        guard let last = viewModel.lastMealPrep else { return }
        let selected = Array(regenState.selectedSlotIds)
        guard !selected.isEmpty else { return }
        isRunningBatch = true
        defer { isRunningBatch = false }

        // Build lookup for slots
        var slotLookup: [UUID: (dayIndex: Int, mealType: MealType, title: String)] = [:]
        for day in last.plan.days {
            for slot in day.meals {
                slotLookup[slot.slotId] = (slot.dayIndex, slot.mealType, slot.recipe.title)
            }
        }

        // Mark as regenerating
        selected.forEach { regenState.setStatus($0, .regenerating) }

        // Build request
        let targets: [RegenerateRequest.TargetSlot] = selected.compactMap { id in
            guard let data = slotLookup[id] else { return nil }
            return .init(slotId: id, dayIndex: data.dayIndex, mealType: data.mealType)
        }
        let exclusions = Array(Set(last.plan.days.flatMap { $0.meals.map { $0.recipe.title } }))
        let request = RegenerateRequest(
            targetSlots: targets,
            originalInputsDays: last.days,
            originalSelectedMeals: last.selectedMeals,
            cuisines: last.cuisines,
            additionalRequest: last.additionalRequest,
            pantryContext: PantryContext(hasItems: pantryService.pantryItems.count > 0, itemCount: pantryService.pantryItems.count),
            exclusions: .init(titles: exclusions)
        )

        // Call adapter
        let adapter = RecipeServiceAdapter(recipeService: ServiceContainer.shared.recipeGenerationService, pantryService: pantryService)
        do {
            let replacementsList = try await adapter.regenerateBatch(request, authService: authService)
            var replacements: [UUID: RecipeUIModel] = [:]
            for (index, slotId) in selected.enumerated() {
                if index < replacementsList.count {
                    replacements[slotId] = replacementsList[index]
                }
            }
            // Save back to PlanStore
            PlanStore.shared.replaceRecipesInLastMealPrep(replacements)
            // Update statuses
            for id in selected {
                if replacements[id] != nil {
                    regenState.setStatus(id, .replaced)
                } else {
                    regenState.setStatus(id, .notReplaced)
                }
            }
            // Refresh view model
            viewModel.reload()
            // Briefly show badges then exit selection
            try? await Task.sleep(nanoseconds: 1_000_000_000)
            withAnimation { regenState.endSelection() }
        } catch {
            // Mark all as not replaced on failure
            for id in selected { regenState.setStatus(id, .notReplaced) }
            try? await Task.sleep(nanoseconds: 1_000_000_000)
        }
    }

    private func formattedDate(_ date: Date, index: Int) -> String {
        if index == 0 { return "Today" }
        if index == 1 { return "Tomorrow" }
        let f = DateFormatter(); f.dateStyle = .medium
        return f.string(from: date)
    }

    private func buildRecipe(from item: RecipeUIModel) -> Recipe {
        // Build meaningful description
        let description = if let subtitle = item.subtitle, !subtitle.isEmpty {
            subtitle
        } else {
            "A delicious \(item.title.lowercased()) recipe perfect for any occasion."
        }

        // Combine pantry and additional ingredients
        var allIngredients: [String] = []
        if let pantryIngredients = item.ingredientsFromPantry {
            allIngredients.append(contentsOf: pantryIngredients)
        }
        if let additionalIngredients = item.additionalIngredients {
            allIngredients.append(contentsOf: additionalIngredients)
        }

        // Generate basic instructions if none provided
        let instructions = if allIngredients.isEmpty {
            [
                "Gather all necessary ingredients for \(item.title).",
                "Follow your preferred cooking method for this dish.",
                "Cook until done and serve hot."
            ]
        } else {
            [
                "Prepare all ingredients: \(allIngredients.prefix(3).joined(separator: ", "))\(allIngredients.count > 3 ? " and others" : "").",
                "Follow standard cooking techniques for \(item.title).",
                "Cook for approximately \(item.estimatedTime ?? 30) minutes.",
                "Season to taste and serve."
            ]
        }

        // Use actual cooking time or default
        let cookingTime = "\(item.estimatedTime ?? 30) minutes"

        // Map difficulty
        let difficulty: Recipe.Difficulty = switch item.difficulty?.lowercased() {
        case "easy": .easy
        case "medium": .medium
        case "hard": .hard
        default: .easy
        }

        return Recipe(
            recipeTitle: item.title,
            description: description,
            ingredients: allIngredients.isEmpty ? ["Ingredients will vary based on your preferences"] : allIngredients,
            instructions: instructions,
            nutrition: .init(calories: "—", protein: "—", carbs: "—", fat: "—"),
            cookingTime: cookingTime,
            servings: item.servings ?? 2,
            difficulty: difficulty
        )
    }
}

