import SwiftUI
import Foundation

// Types moved to Models/ and Services/ to avoid duplication.


@Observable
@MainActor
class RecipeGeneratorViewModel {
    private let recipeService: RecipeGenerationServiceProtocol
    private let authService: AuthenticationService
    var navigationCoordinator: NavigationCoordinator?

    // P0 Core State (aligned with PRD 2.1)
    var mode: UIMode = .quick
    var customConfiguration: CustomConfiguration = .init()
    var quickConfiguration: QuickConfiguration = .init()
    var viewState: ViewState = .idle
    var pantryState: PantryState = .loading

    // Generation task handle (P0 2.3)
    private var currentGenerationTask: Task<Void, Never>?

    // Cache custom config when switching modes (P0 2.4)
    private var cachedCustomConfiguration: CustomConfiguration?

    var canGenerate: Bool {
        // Pantry must have items
        guard case .hasItems = pantryState else { return false }
        // Not while loading
        if case .loading = viewState { return false }
        switch mode {
        case .quick:
            // Quick: minimal inputs; defaults are valid
            return true
        case .custom:
            // V4: Relax enablement — only core required fields
            let maxDays = RemoteConfigurationManager.shared.configuration.maxDays
            guard !customConfiguration.selectedMeals.isEmpty else { return false }
            guard (1...maxDays).contains(customConfiguration.days) else { return false }
            // Do not require per-meal configs here; we will sanitize before building the request
            return true
        }
    }

    // P1 2.5: Configuration summary for Custom mode (Phase 1 trimmed)
    var configurationSummary: String {
        guard mode == .custom else { return "" }
        if customConfiguration.selectedMeals.isEmpty {
            return NSLocalizedString("config_summary_empty", comment: "Please select at least one meal")
        }

        let mealsText = customConfiguration.selectedMeals.map { $0.displayName }.joined(separator: ", ")
        let daysText = customConfiguration.days == 1
            ? NSLocalizedString("config_summary_one_day", comment: "1 day")
            : String(format: NSLocalizedString("config_summary_days", comment: "%d days"), customConfiguration.days)

        let summary = String(format: NSLocalizedString("config_summary_planning", comment: "Planning %@ of %@"), daysText, mealsText)
        return summary
    }

    // Pantry state provider injection
    private let pantryStateProvider: PantryStateProvider = DefaultPantryStateProvider(pantryService: ServiceContainer.shared.pantryService)

    init(
        recipeService: RecipeGenerationServiceProtocol = ServiceContainer.shared.recipeGenerationService,
        authService: AuthenticationService = ServiceContainer.shared.authenticationService
    ) {
        self.recipeService = recipeService
        self.authService = authService

        // Observe pantry state changes
        Task { [weak self] in
            guard let self = self else { return }
            for await state in pantryStateProvider.observePantryChanges() {
                self.pantryState = state
            }
        }

        // Initialize pantry state
        Task { [weak self] in
            guard let self = self else { return }
            self.pantryState = await pantryStateProvider.checkPantryState()
        }
    }

    /// Get user's dietary restrictions as strings (fixed allergies/intolerances accessor per Phase 1)
    var userDietaryRestrictions: [String] {
        if let preferences = authService.userPreferences {
            let restrictions = preferences.dietaryRestrictions.map { $0.rawValue }
            let allergiesIntolerances = preferences.allergiesIntolerances.map { $0.rawValue }
            let strictExclusions = preferences.strictExclusions.map { $0.rawValue }

            let allRestrictions = restrictions + allergiesIntolerances + strictExclusions
            return Array(Set(allRestrictions)) // Remove duplicates
        }
        return []
    }

    /// Generate user preferences context string for recipe generation
    var userPreferencesContext: String {
        guard let preferences = authService.userPreferences else { return "" }

        var context = ""

        // Family size
        context += "- Family size: \(preferences.familySize) people\n"

        // Dietary restrictions
        if !preferences.dietaryRestrictions.isEmpty {
            context += "- Dietary preferences: \(preferences.dietaryRestrictions.map { $0.rawValue }.joined(separator: ", "))\n"
        }

        // Allergies and Intolerances (fixed accessor per Phase 1)
        if !preferences.allergiesIntolerances.isEmpty {
            context += "- Allergies/Intolerances: \(preferences.allergiesIntolerances.map { $0.rawValue }.joined(separator: ", "))\n"
        }

        // Strict exclusions
        if !preferences.strictExclusions.isEmpty {
            context += "- Must exclude: \(preferences.strictExclusions.map { $0.rawValue }.joined(separator: ", "))\n"
        }

        // Kitchen equipment (Phase 2)
        if !preferences.equipmentOwned.isEmpty {
            context += "- Available equipment: \(preferences.equipmentOwned.joined(separator: ", "))\n"
        }

        return context
    }


    /// Public API used by UI (kept for compatibility). Starts a cancellable task and awaits its completion.
    func generateRecipeIdeas(cookingTimeMinutes: Int) async {
        await generateRecipes(cookingTimeMinutes: cookingTimeMinutes)
    }

    /// New P0 API: create task handle, animate to loading, and perform generation.
    func generateRecipes(cookingTimeMinutes: Int) async {
        // Cancel any in-flight task
        currentGenerationTask?.cancel()
        withAnimation { viewState = .loading }
        let task = Task { [weak self] in
            guard let self else { return }
            await self.performGeneration(cookingTimeMinutes: cookingTimeMinutes)
        }
        currentGenerationTask = task
        // For tests/UI awaiting completion
        _ = await task.value
    }

    /// Cancel current generation task and reset state with haptic feedback.
    func cancelGeneration() {
        currentGenerationTask?.cancel()
        currentGenerationTask = nil
        withAnimation { viewState = .idle }
        Haptics.light()
    }

    private func performGeneration(cookingTimeMinutes: Int) async {
        // Early exit if cancelled
        if Task.isCancelled { return }
        // Build request with user equipment (Phase 2)
        let userEquipment = authService.userPreferences?.equipmentOwned ?? []
        let buildResult = RecipeRequestBuilder.build(mode: mode, custom: customConfiguration, quick: quickConfiguration, pantryState: pantryState, userEquipment: userEquipment)
        switch buildResult {
        case .failure(let e):
            if Task.isCancelled { return }
            switch e {
            case .pantryEmpty:
                viewState = .failed(.pantryEmpty)
            case .invalidConfiguration(let reason):
                viewState = .failed(.configurationError(reason))
            }
        case .success(let request):
            do {
                if Task.isCancelled { return }
                let adapter = RecipeServiceAdapter(recipeService: recipeService, pantryService: ServiceContainer.shared.pantryService)
                let uiModels = try await adapter.generate(using: request, cookingTimeMinutes: cookingTimeMinutes, authService: authService)
                if Task.isCancelled { return }
                // Phase 2: persist results and auto-switch to Recipes tab
                viewState = .loaded(uiModels)
                if mode == .quick {
                        PlanStore.shared.saveLastQuick(config: quickConfiguration, recipes: uiModels)
                    } else {
                        PlanStore.shared.saveLastMealPrep(config: customConfiguration, recipes: uiModels)
                    }
                navigationCoordinator?.switchToTab(3)
            } catch {
                if Task.isCancelled { return }
                viewState = .failed(.serviceError(message: error.localizedDescription))
            }
        }
    }

    // MARK: - Mode switching (P0 2.4)

    func selectMode(_ newMode: UIMode) {
        guard newMode != mode else { return }
        // Cache current custom config when leaving custom
        if mode == .custom { cachedCustomConfiguration = customConfiguration }
        cancelGeneration()
        withAnimation(.easeInOut) {
            mode = newMode
            // Restore cached custom config when entering custom
            if newMode == .custom, let cached = cachedCustomConfiguration {
                customConfiguration = cached
            }
            viewState = .idle
        }
    }



    /// Clear results (reset state)
    func clearResults() {
        viewState = .idle
    }

    /// Format cooking time for display
    func formatCookingTime(_ minutes: Int) -> String {
        if minutes < 60 {
            return "\(minutes) min"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            if remainingMinutes == 0 {
                return "\(hours) hr"
            } else {
                return "\(hours) hr \(remainingMinutes) min"
            }
        }
    }

    /// Check if recipe matches user preferences
    func isRecipeCompatible(with recipe: Recipe) -> Bool {
        guard authService.userPreferences != nil else { return true }

        // Check against strict exclusions and allergies
        let userRestrictions = Set(userDietaryRestrictions.map { $0.lowercased() })
        let recipeIngredients = Set(recipe.ingredients.map { $0.lowercased() })

        // If any user restriction appears in recipe ingredients, it's not compatible
        return userRestrictions.isDisjoint(with: recipeIngredients)
    }
}