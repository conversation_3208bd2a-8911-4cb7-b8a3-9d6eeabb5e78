# V4 Tasks: Implementation Guide

This folder contains actionable tasks derived from PRD_Scan_Pantry_V4.md. Each task is self-contained with scope, file pointers, acceptance criteria, and validation steps. Complete tasks in order unless noted.

Repo assumptions
- iOS 17+ (SwiftUI, NavigationStack, @Observable)
- No changes to authentication/profile sync
- Build in Xcode 15+

Workflow
1) Create a feature branch per task (e.g., feature/v4-task-01-pantry-highlights)
2) Make minimal, focused changes
3) Run build/tests locally
4) Open PR; get review; merge

Useful paths
- Pantry UI: Features/Pantry
- Scan/Results: Features/1_ImageCapture, Features/3_Results
- Services: Services/*
- Models: Models/*
- Tests: Tests/*

Validation
- Prefer unit/UI tests where noted
- Manual smoke: launch app, run through scenarios per task

Tasks index
- TASK_01_Pantry_Highlights.md
- TASK_02_Add_Sheet_Immediate_Add_Custom_Gemini.md
- TASK_03_PantryService_Duplicates.md
- TASK_04_Generator_Recipes_Fixes.md
- TASK_05_Gemini_Prompts_Scan_Organizer.md
- TASK_06_Tests.md
- TASK_07_Kitchen_Equipment.md

Notes
- Keep diffs small and reversible
- Do not introduce new dependencies without approval
- Follow the PRD strictly (V4 Tasks/PRD_Scan_Pantry_V4.md)

