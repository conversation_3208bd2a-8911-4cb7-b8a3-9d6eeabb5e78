# PRD: V4 Scan, <PERSON>try, and Recipes Fixes and Refinements

Version: 4.0
Owner: You
Branch target: hao2 (then merge to kuo/main after verification)
Platforms: iOS 17+ (SwiftUI, NavigationStack, @Observable, async/await)

---

## 1) Summary

This PRD consolidates fixes and refinements discovered during device testing of hao2. It aligns the app with the intended scan→results→pantry flow, Pantry add UX, and the Recipes/Generator behavior. Key items:
- Pantry: highlight recently added items (from scan and manual), improve Add sheet (instant add, auto-clear, Add custom with Gemini cleanup), keep duplicates on save, rely on“Organize Pantry” for merges later.
- Scan results: after adding, pantry highlights must appear; remove Generator in-tab results.
- Recipes tab: items must be tappable to show full detail.
- Generator tab: rename Custom → Meal Plan; ensure Generate button enables with minimal required inputs.
- Gemini prompts: refine scan cleanup and organizer to better normalize pluralization/noise; strict category match only.

---

## 2) Goals

- Make “recently added” visually clear in Pantry for ~5 seconds after adds.
- Streamline manual Add: instant add on suggestion tap; auto-clear text; support Add custom entries and send custom list to <PERSON> at Save.
- Allow duplicates on save; no local name normalization.
- Ensure Results add triggers pantry highlights.
- Keep recipes only in Recipes tab and navigate to detail on tap.
- Fix Generator tab labels and enablement logic.
- Add/adjust tests to cover these.

---

## 3) Out of Scope

- Auth/profile sync changes
- Theme/notifications settings
- Deleting or changing IngredientLibrary inventory itself

---

## 4) Detailed Requirements

### A) Pantry highlights
- When adding via Results or Pantry Add sheet:
  - Call PantryService.markAsRecentlyAdded(newItems)
  - PantryView renders highlighted state for isRecentlyAdded(item) with a subtle tinted background or halo.
  - Recently added set auto-clears after ~5s (already implemented) and on leaving Pantry tab.

### B) Pantry Add sheet UX
- Suggestions from IngredientLibrary:
  - Tap suggestion → immediately add to a Selected list and clear the TextField (keep focus).
  - Show ephemeral pill: “Added: <name>”.
- Add custom ingredient:
  - If current query has no library match, show a top row: Add “<query>” as custom ingredient.
  - Tapping adds to a Custom Inputs list in-sheet; clear TextField.
- Save behavior:
  - Build libraryItems via IngredientLibrary.shared.group(Array(selectedNames))
  - Call GeminiAPIService.canonicalizeCustomIngredients(customInputs, allowedCategories: PantryCategory.allCases.map(\.rawValue))
  - Merge results: finalItems = libraryItems + cleanedCustomItems
  - await pantryService.addIngredients(finalItems)
  - pantryService.markAsRecentlyAdded(finalItems)
  - Dismiss sheet
- Failure:
  - If Gemini fails for custom list: save library items; keep customInputs for retry; show banner.

### C) Duplicates policy
- PantryService: remove dedupe/merge on save; allow duplicates.
- Organizer remains the only merging mechanism.

### D) Scan Results integration
- ResultsViewModel.addSelectedToPantry(): after save, call pantryService.markAsRecentlyAdded(selectedIngredients).

### E) Recipes and Generator tabs
- Remove any results rendering from Generator tab.
- Recipes tab: tapping a recipe opens GeneratedRecipeDetailView.
- Generator UI: change “Custom” → “Meal Plan”.
- Enablement: Relax canGenerate to match V3 rules; Quick: minimal fields; Meal Plan: only core fields required.

### F) Prompt refinements
- Scan canonicalization: emphasize removal of brand/size/qty/marketing and normalize simple pluralization/singularization while preserving meaningful descriptors; strict category match; return JSON only.
- Organizer: reinforce duplicate merging via merge groups; strict category list; remove non-food/gibberish; JSON-only plan.

---

## 5) UX Notes
- Pantry highlight: background tint or halo that does not fight with expiration warning.
- Add sheet: keep search focus after any add; support chips for Selected (library) and Custom Inputs; allow removing chips.
- Brief success summary after Organize (existing), unchanged here.

---

## 6) Technical Scope

Files to modify (indicative):
- Features/Pantry/PantryView.swift
  - Render highlight using pantryService.isRecentlyAdded
  - Add suggestion-tap immediate add and auto-clear
  - Add “Add custom ingredient” row and Custom Inputs chips
- Features/Pantry/PantryViewModel.swift (or sheet-local state)
  - Track customInputs; implement onSave building libraryItems + Gemini cleanup
- Services/GeminiAPIService.swift
  - Add canonicalizeCustomIngredients([...]) endpoint and parser
- Services/PantryService.swift
  - Remove dedupe/merge on save paths
- Features/3_Results/ResultsViewModel.swift
  - Call markAsRecentlyAdded after add
- Features/RecipeGenerator/RecipeGeneratorView*.swift
  - Remove results in Generator tab; rename label; adjust canGenerate conditions
- Features/Recipes/RecipesView.swift
  - Ensure NavigationLink/onTap navigates to GeneratedRecipeDetailView

Optional:
- Visual polish variables (tint colors) in Shared/Theme if present

---

## 7) Error Handling
- Gemini failure during custom list cleanup: save library picks; banner + retain customInputs for retry.
- Vision/Gemini scan flow unchanged here.

---

## 8) Testing
- Unit
  - canonicalizeCustomIngredients: accept only PantryCategory; names pass-through
  - PantryService: ensure no dedupe on save
  - Organizer merge plan application (existing tests may cover; extend if needed)
- UI
  - Add sheet: tap suggestion adds and clears; custom row appears when no match; Save cleans custom with Gemini and highlights items
  - Pantry: recently added highlight appears and clears after ~5s
  - Generator: label “Meal Plan”; Generate enabled with minimal inputs; no results displayed in this tab
  - Recipes: tap navigates to detail

---

## 9) Delivery Plan (PR sequence)
1) Pantry highlights + Results integration
2) Add sheet: instant add + auto-clear + custom row + chips (UI only)
3) GeminiAPIService.canonicalizeCustomIngredients and wire Save path
4) PantryService: remove dedupe/merge on save
5) Generator/Recipes tab fixes (labels, canGenerate, results placement, navigation)
6) Tests and polish

---

## 10) Risks
- More duplicates until Organizer runs: acceptable per direction
- Custom cleanup failure path: ensure graceful save of library picks
- Token budget: custom list sizes are small; batch in one call

---

## 11) Acceptance Criteria
- Pantry: new items highlight after add (scan and manual); clears after ~5s
- Add sheet: suggestion tap adds and clears; custom row appears; Save cleans custom via Gemini and saves all; highlights visible
- PantryService: duplicates allowed on save (no dedupe/merge)
- Generator: label shows “Meal Plan”; Generate enabled as per V3; no in-tab results
- Recipes: items tappable to detail view

