# TASK 05: Gemini Prompts – Scan Cleanup and Organizer

Objective
- Refine scan cleanup and organizer prompts to fit V4 rules: strict category match; better pluralization/name cleanup; no local normalization.

Scope
- Services/GeminiAPIService.swift (prompt builders, parsers)
- Services/PantryOrganizerService.swift (organize prompt builder)

Steps
1) Scan canonicalization prompt
- Keep strict category constraint: category ∈ PantryCategory.rawValue list.
- Add guidance to remove brand/size/qty/marketing and normalize simple pluralization, preserving meaningful descriptors.
- Output JSON only; names pass-through; drop items with non‑matching categories.

2) Custom inputs canonicalization
- Implement canonicalizeCustomIngredients(names:[String], ...): reuse scan constraints, simpler input payload.

3) Organizer prompt
- Emphasize merge groups for duplicates; strict categories; removal of non‑food/gibberish.
- Output JSON plan {updates, removals, merges}.

Acceptance Criteria
- Parsers clamp categories; names are not locally normalized; duplicates left to Organizer.
- Organizer returns reasonable merges for common variants.

Validation
- Unit tests for parsers with edge cases (pluralization variants, brand/size noise, non‑food dropped).

