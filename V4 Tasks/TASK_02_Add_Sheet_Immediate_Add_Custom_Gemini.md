# TASK 02: Add Sheet – Immediate Add, Auto‑Clear, Custom + Gemini Cleanup

Objective
- Make Pantry Add sheet low‑friction: instant add on suggestion tap; auto‑clear text; support custom entries buffered as "Custom Inputs" and cleaned/categorized by Gemini at Save.

Scope
- Features/Pantry/PantryView.swift (Add sheet UI + handlers)
- Features/Pantry/PantryViewModel.swift (state for customInputs and onSave wiring)
- Services/GeminiAPIService.swift (new method)

Steps
1) Add sheet UI
- When search has no library match and is non‑empty, show a top row: Add "<query>" as custom ingredient.
- Maintain chips for Selected (library) and Custom Inputs (free‑form). Each chip removable.
- On suggestion tap: add to Selected immediately, clear TextField, keep focus, show "Added: <name>" pill.
- On Add custom tap: append to Custom Inputs, clear TextField, keep focus.

2) ViewModel logic
- Track customInputs: [String].
- onSave():
  - libraryItems = IngredientLibrary.shared.group(Array(selectedNames)).map(Ingredient.init)
  - cleanedCustom = try await GeminiAPIService.shared.canonicalizeCustomIngredients(customInputs, allowedCategories: PantryCategory.allCases.map(\.rawValue))
  - finalItems = libraryItems + cleanedCustom
  - await pantryService.addIngredients(finalItems)
  - pantryService.markAsRecentlyAdded(finalItems)
  - dismiss()
- On Gemini failure: save libraryItems only; keep customInputs; show banner.

3) GeminiAPIService
- Implement canonicalizeCustomIngredients(names:[String], allowedCategories:[String]) async throws -> [Ingredient]
- Prompt: strict JSON; category must be exactly one of allowedCategories; clean brand/size/qty; exclude non‑food/gibberish; preserve meaningful descriptors.

Acceptance Criteria
- Suggestion tap adds instantly and clears the field.
- Custom row appears when there is no match; tapping adds to Custom Inputs and clears the field.
- Save cleans/categorizes custom inputs via Gemini and saves both sets; highlights show in Pantry.
- On Gemini failure, library items still save; custom kept for retry with an error banner.

Validation
- Manual: Try both library and custom; ensure UX matches.
- Unit: parser for canonicalizeCustomIngredients enforces category clamp and passes names through.

