# TASK 04: Generator/Recipes – Placement, Navigation, Labels, Enablement

Objective
- Keep results out of Generator tab; ensure <PERSON><PERSON><PERSON> lists navigate to detail; rename Custom → Meal Plan; ensure Generate button enabled with minimal inputs.

Scope
- Features/RecipeGenerator/*
- Features/Recipes/RecipesView.swift

Steps
1) Generator tab
- Remove any inline results rendering; Generator is inputs only.
- Rename UI labels: "Custom" → "Meal Plan" (segmented/picker and headers).
- Review canGenerate rules in RecipeGeneratorViewModel; relax per V4 PRD: Quick minimal; Meal Plan only core required fields.

2) Recipes tab
- Ensure items are tappable (NavigationLink or onTap) to GeneratedRecipeDetailView.
- Verify Favorites integration remains intact.

Acceptance Criteria
- Generator tab shows inputs only; no results list present.
- Recipes tab taps open detail.
- Segments/labels show "Meal Plan".
- Generate button is enabled when minimal requirements are met.

Validation
- Manual: Generate; auto-switch to Recipes; open detail; return to Generator and verify no results shown.
- Unit/UI: canGenerate logic tests; simple navigation test if present.

