# TASK 03: PantryService – Allow Duplicates on Save

Objective
- Remove dedupe/merge during save so near‑duplicate variants can exist; rely on Organizer to merge later.

Scope
- Services/PantryService.swift

Steps
1) In addIngredients and addIngredient, remove logic that merges/dedupes by (name, category) and calls mergeDuplicates().
2) Ensure dateAdded/purchaseDate are set appropriately for newly created items.
3) Keep markAsRecentlyAdded behavior unchanged.

Acceptance Criteria
- Adding "butter croissant" and "butter croissants" results in two entries (until Organizer runs).
- No implicit merge occurs during save.

Validation
- Unit test: add two near‑duplicates and assert both exist.

