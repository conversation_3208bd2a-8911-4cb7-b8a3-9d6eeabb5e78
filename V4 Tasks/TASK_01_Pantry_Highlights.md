# TASK 01: Pantry Highlights (Scan + Manual)

Objective
- Ensure newly added items are visually highlighted in Pantry for ~5 seconds whether added from Scan Results or Pantry Add sheet.

Scope
- Features/Pantry/PantryView.swift
- Features/3_Results/ResultsViewModel.swift
- Services/PantryService.swift (usage only)

Steps
1) PantryView: pass isRecentlyAdded = pantryService.isRecentlyAdded(ingredient) into cell rendering and apply a subtle highlight (tinted background/halo) when true.
2) ResultsViewModel.addSelectedToPantry(): after await pantryService.addIngredients(selectedIngredients), call pantryService.markAsRecentlyAdded(selectedIngredients).
3) Verify manual Add flow already calls markAsRecentlyAdded(items) on Save; keep it.

Acceptance Criteria
- Adding from Results highlights items in Pantry for ~5s.
- Adding from Pantry Add sheet highlights items for ~5s.
- Highlights auto-clear when leaving Pantry tab (existing behavior) and/or after 5s.

Validation
- Manual: Add from Results and Add sheet; observe highlight.
- Optional UI test: assert highlight state toggles within 5s window.

