# Ingredient Scanner – Meal Plan AI Workflow and Issues Report

Date: 2025-09-02
Owner: Augment Agent (Augment Code)
Repository path: /Users/<USER>/Desktop/ingredient-scanner

## 1) Executive Summary

- Number of AI calls in standard flow:
  - Generation (from Generator to Recipes list): 1 Gemini request to produce recipe ideas/menu.
  - Opening a recipe detail from Recipes (e.g., Today section): On first open per title, 1 Gemini request to generate detailed recipe content; subsequent opens use in-memory cache and do not call AI again.
  - Regenerate flow (Phase 3): 1 request; if replacements are insufficient, a second request is made (max 2 for that action).
- Why clicking Today shows “Generating detailed recipe…”: entering the recipe detail view triggers the first-time detail-generation call (cached afterwards).
- Why selecting 2 dishes sometimes yields only 1: the current prompt in RecipeGenerationService is hard-coded to “Generate 3 recipes” and does not propagate the UI-selected dish count to the Gemini prompt. Additionally, post-filtering by restrictions can reduce the number of results.
- Inputs considered when calling AI:
  - Pantry-only ingredients (from PantryService)
  - Profile: familySize (servings), dietaryRestrictions, allergiesIntolerances, strictExclusions (only if respectRestrictions = true), equipmentOwned
  - UI: cuisines, additionalRequest, quick/custom cooking time; request-level equipment overrides

## 2) End-to-End Workflow (Generator ➜ Recipes)

### 2.1 Trigger
- User configures options in Generator (Quick or Custom):
  - Quick: numberOfDishes, totalTimeMinutes, mealType, cuisines, additionalRequest
  - Custom: days, selectedMeals, per-meal cooking time and dish count, cuisines, additionalRequest
- View-model: Features/RecipeGenerator/RecipeGeneratorViewModel.swift → generateRecipes(cookingTimeMinutes:)

### 2.2 Request Build
- Services/RecipeRequestBuilder.swift builds RecipeGenerationRequest with:
  - PantryContext: hasItems + itemCount
  - RequestDetails:
    - Schedule: startDate (today), days
    - Meals: [MealRequest(type, maxCookingTimeMinutes, numberOfDishes)]
    - Preferences: cuisines, additionalRequest, equipmentOwned (from user profile at this stage)

### 2.3 Adapter (Profile Overlay + Pantry Enforcement)
- Services/RecipeServiceAdapter.swift
  - Collect pantryIngredients = PantryService.pantryItems names; if empty → fail
  - Build RecipePreferences from AuthenticationService.userPreferences:
    - servings = familySize
    - if respectRestrictions: inject dietaryRestrictions, allergiesIntolerances, strictExclusions; set respectRestrictions = true
    - equipmentOwned = userPrefs.equipmentOwned
  - Overlay request-level preferences: cuisines, additionalRequest, equipmentOwned (request overrides profile if provided)
  - Call recipeService.generateMealIdeas(from: pantryIngredients, preferences)

### 2.4 AI Generation (Ideas/Menu)
- Services/RecipeGenerationService.swift
  - createRecipePrompt(from: ingredients, preferences):
    - Hard-coded: “Generate 3 healthy recipes … Return the response as a JSON array …”
    - Includes: Target servings (familySize), Target cooking time (~preferences.cookingTimeInMinutes), constraints from restrictions/exclusions, equipmentOwned when present
  - processRecipeText(prompt): POST to Gemini 1.5 Flash generateContent endpoint
  - extractJSON + decode [RecipeData] → map to [Recipe]
  - Post-filter: if respectRestrictions, drop recipes containing allergens/strictExclusions
  - Map to [RecipeIdea]

### 2.5 Mapping & Persistence
- Adapter maps [RecipeIdea] to [RecipeUIModel], with pantry-only ingredients listed under ingredientsFromPantry and additionalIngredients forced empty.
- ViewModel persists:
  - Quick: PlanStore.saveLastQuick(config, recipes)
  - Custom: PlanStore.saveLastMealPrep(config, recipes)
- Recipes tab shows lastQuick/lastMealPrep via Features/Recipes/RecipesViewModel.swift; no AI calls here.

### 2.6 Opening a Recipe Detail (Today ➜ Detail)
- Features/RecipeGenerator/GeneratedRecipeDetailView.swift
  - On appear (.task): fetchRecipeDetail()
  - Lookup RecipeDetailCache (in-memory); if miss, call GeminiAPIService.generateRecipeDetail(title, cuisines?, equipmentOwned from profile), then cache result; otherwise use cached detail
  - This is why entering detail shows “Generating detailed recipe…”. It is a second, separate AI call but only the first time for each title.

### 2.7 Regenerate Flow (Phase 3)
- Features/Recipes/… actions trigger RecipeServiceAdapter.regenerateBatch:
  - Build preferences similar to generate(); call recipeService.generateMealIdeas once
  - If produced replacements < needed, call generateMealIdeas a second time and append additional results
  - Save replacements to PlanStore

## 3) AI Call Points & Counts

- Ideas/Menu generation: 1 request per Generate action (Quick/Custom)
- Detail generation: 0 or 1 request per title view, depending on cache
- Regenerate batch: 1 request; if insufficient count, +1 retry (max 2)
- Recipes list UI and navigating to Today section: no AI calls until you enter a recipe detail

## 4) Inputs and Constraints Considered by AI

- Pantry inputs: strictly use PantryService.pantryItems; additionalIngredients are forced empty at UI mapping
- Profile-derived preferences:
  - numberOfServings = userPreferences.familySize
  - if userPreferences.respectRestrictions == true:
    - dietaryRestrictions appended as contextual preferences
    - allergiesIntolerances and strictExclusions enforced (post-filtering also removes violating items)
  - equipmentOwned: passed both at ideas generation (preferences.equipmentOwned) and detail generation
- UI/request-level preferences:
  - cuisines
  - additionalRequest (free text)
  - cookingTimeMinutes (Quick: totalTimeMinutes; Custom: per-meal maxCookingTimeMinutes flows into RequestDetails, but generation prompt uses a single cookingTimeInMinutes value from the view-model parameter)
  - equipmentOwned from request may override profile equipment in Adapter

## 5) Issues Observed

### 5.1 Detail view triggers AI call unexpectedly (from user perspective)
- Cause: design choice to lazy-generate detailed recipe content per title on first open; only cached in-memory (no disk persistence/TTL)
- Symptom: User perceives another generation when tapping Today ➜ detail

### 5.2 Selecting 2 dishes but seeing only 1 recipe in results
- Root causes:
  1) Dish count from UI is not propagated into the Gemini prompt; prompt is hard-coded to “Generate 3 …”
  2) Gemini may output fewer items than requested and we accept whatever length is returned
  3) Post-filtering by restrictions can remove some results if they include allergens/strict exclusions
  4) In Quick mode, request has numberOfDishes, but Adapter/Service do not use it to control prompt or result validation

## 6) Remedies (Proposed Solutions)

### 6.1 Make result count match the requested “Dishes”
- Propagate requested N
  - Read numberOfDishes from RequestDetails.MealRequest; carry N into RecipePreferences or pass as an explicit parameter to RecipeGenerationService.generateMealIdeas
  - Update createRecipePrompt to use N instead of the hard-coded 3
- Stronger prompt contract
  - “Generate exactly N recipes … Return only a compact JSON array with exactly N objects, no surrounding text.”
  - Include an example with N=2 in the prompt to prime the model
- Response-mode JSON
  - Set generationConfig.response_mime_type = "application/json" (Gemini supports this) to reduce surrounding text and ease parsing
- Output validation & retry
  - If decoded array.count < N: re-issue one retry with added guidance like “Missing X more unique recipes not similar to {already_titles}”
  - If still < N: optionally synthesize simple variants from pantry (fallback) or surface a user-facing message “因限制仅生成了 X 道菜”
- Unit tests
  - Add tests ensuring for N=1..6 we get exactly N RecipeUIModel unless restrictions eliminate items, in which case retry once then fall back with clear messaging

### 6.2 Detail generation UX/control
- Prefetch strategy
  - After menu generation, prefetch top K (e.g., 1–3) recipe details in background and cache; most likely taps become instant
- On-demand strategy
  - Do not auto-generate details on opening; show a “生成详细做法” button to control costs; optionally long-press from list to prefetch
- Persistent caching
  - Persist RecipeDetailCache to disk with a TTL (e.g., 24–72h) keyed by title+hash(profile equipment + cuisines) to maximize reuse and avoid re-calls after app restarts
- Telemetry
  - Instrument counters for idea-generation calls and detail-generation calls; attach correlation IDs and request metadata (N, cuisines, equipment) for debugging

### 6.3 Robustness & Safety
- Post-filtering before mapping
  - If post-filtering removes items, attempt a retry to fill back to N
- Schema guardrails
  - Validate each recipe object has mandatory fields; if missing, prompt a repair pass (ask Gemini to convert to strict JSON)

## 7) Suggested Implementation Plan (Minimal First)

1) Count propagation & prompt fix
   - Adapter: read numberOfDishes (Quick/custom per-meal); decide one effective N for current generation mode
   - RecipeGenerationService.generateMealIdeas: accept parameter requestedCount: Int
   - createRecipePrompt: use requestedCount in “Generate exactly N recipes …”
2) Output validation & single retry
   - After decode, if count < N → retry once; still不足则反馈 UI 或补齐
3) Detail UX optimization
   - Prefetch first recipe detail after generation completion; keep the rest on-demand
   - Add simple disk-backed cache with TTL
4) Add telemetry & tests
   - Unit tests for N enforcement and restriction-induced reductions
   - Lightweight logging of AI call counts (debug builds)

## 8) File/Function References (for developers)
- Generator flow:
  - Features/RecipeGenerator/RecipeGeneratorViewModel.generateRecipes()
  - Services/RecipeRequestBuilder.build(...)
  - Services/RecipeServiceAdapter.generate(...)
  - Services/RecipeGenerationService.generateMealIdeas(...)
- AI endpoints:
  - Services/RecipeGenerationService.processRecipeText(...)
  - Services/GeminiAPIService.generateRecipeDetail(...)
- Caching:
  - Services/RecipeDetailCache
- Recipes list:
  - Features/Recipes/RecipesViewModel.reload()
  - Features/Recipes/RecipesView
- Regenerate:
  - Services/RecipeServiceAdapter.regenerateBatch(...)

## 9) Open Questions / Decisions Needed
- When multiple meals/days are selected in Custom mode, do we generate per-meal counts separately (e.g., breakfast 2, dinner 3) or a single merged list size? Current P0 treats as aggregated ideas without strict per-meal slots.
- Prefetch budget: how many details may we prefetch without surprising costs? Suggest K=1–2.
- Persistence scope for detail cache: in-memory only vs on-disk with TTL.

## 10) Appendix: Observed UX Screens
- “Generating detailed recipe …” during first open of a recipe detail is expected by current design due to lazy detail generation.

— End of Document —

