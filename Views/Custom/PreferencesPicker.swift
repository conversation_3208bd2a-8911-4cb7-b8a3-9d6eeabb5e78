import SwiftUI

/// Multi-select chips for cuisines (Phase 1)
struct CuisinesChips: View {
    @Binding var selected: [String]
    var allCuisines: [String] {
        RemoteConfigurationManager.shared.configuration.availableCuisines
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cuisines").font(.headline)
            FlowLayout(alignment: .leading, spacing: 8) {
                ForEach(allCuisines, id: \.self) { cuisine in
                    let isSelected = selected.contains(cuisine)
                    Button(action: {
                        if isSelected {
                            selected.removeAll { $0 == cuisine }
                        } else {
                            selected.append(cuisine)
                        }
                    }) {
                        Text(cuisine)
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(isSelected ? Color.accentColor.opacity(0.2) : Color.secondary.opacity(0.1))
                            .foregroundColor(isSelected ? .accentColor : .primary)
                            .clipShape(Capsule())
                    }
                    .buttonStyle(.plain)
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

/// Minimal flow layout for chips
struct FlowLayout<Content: View>: View {
    let alignment: HorizontalAlignment
    let spacing: CGFloat
    @ViewBuilder var content: () -> Content

    var body: some View {
        VStack(alignment: alignment, spacing: spacing) {
            _FlowLayout(spacing: spacing, content: content)
        }
    }
}

private struct _FlowLayout<Content: View>: View {
    let spacing: CGFloat
    @ViewBuilder var content: () -> Content

    var body: some View {
        var width: CGFloat = 0
        var height: CGFloat = 0
        return GeometryReader { geometry in
            ZStack(alignment: .topLeading) {
                content()
                    .padding(4)
                    .alignmentGuide(.leading) { d in
                        if (abs(width - d.width) > geometry.size.width) {
                            width = 0
                            height -= d.height + spacing
                        }
                        let result = width
                        if content() is EmptyView { width = 0 } else { width -= d.width + spacing }
                        return result
                    }
                    .alignmentGuide(.top) { _ in height }
            }
        }
        .frame(minHeight: 0)
    }
}

#Preview {
    @Previewable @State var selected: [String] = []
    CuisinesChips(selected: $selected)
        .padding()
}

