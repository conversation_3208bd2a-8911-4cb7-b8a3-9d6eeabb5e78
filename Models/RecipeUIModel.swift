import Foundation

struct RecipeUIModel: Identifiable, Hashable, Codable {
    let id: String
    let title: String
    let subtitle: String?
    let estimatedTime: Int?
    let imageURL: String?
    let ingredientsFromPantry: [String]?
    let additionalIngredients: [String]?
    let difficulty: String?

    // Enhanced properties for grouping (PRD 9.2)
    let mealType: MealType?
    let dayIndex: Int? // 0-based day index for multi-day plans
    let servings: Int?
    let cuisine: String?

    init(
        id: String,
        title: String,
        subtitle: String? = nil,
        estimatedTime: Int? = nil,
        imageURL: String? = nil,
        ingredientsFromPantry: [String]? = nil,
        additionalIngredients: [String]? = nil,
        difficulty: String? = nil,
        mealType: MealType? = nil,
        dayIndex: Int? = nil,
        servings: Int? = nil,
        cuisine: String? = nil
    ) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.estimatedTime = estimatedTime
        self.imageURL = imageURL
        self.ingredientsFromPantry = ingredientsFromPantry
        self.additionalIngredients = additionalIngredients
        self.difficulty = difficulty
        self.mealType = mealType
        self.dayIndex = dayIndex
        self.servings = servings
        self.cuisine = cuisine
    }
}

// MARK: - Grouping Models (PRD 9.2)

struct RecipeResultsData {
    let sections: [DaySection]
    let summary: GenerationSummary
}

struct DaySection: Identifiable {
    let id = UUID()
    let dayIndex: Int // 0-based
    let date: String // YYYY-MM-DD
    let displayDate: String // "Today", "Tomorrow", "Aug 25"
    let meals: [MealSection]
}

struct MealSection: Identifiable {
    let id = UUID()
    let mealType: MealType
    let displayName: String // "Breakfast", "Lunch", "Dinner"
    let dishes: [RecipeUIModel]
    let totalTime: Int // Total cooking time for this meal
    let pantryUsage: PantryUsage
}

struct PantryUsage {
    let itemsUsed: Int
    let itemsTotal: Int
    let utilizationRate: Double // 0.0 - 1.0
}

struct GenerationSummary {
    let totalRecipes: Int
    let totalDays: Int
    let avgCookingTime: Int
    let pantryUtilization: Double
    let cuisineDistribution: [String: Int]
}

