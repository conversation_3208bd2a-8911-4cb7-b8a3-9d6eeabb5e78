import Foundation
@preconcurrency import FirebaseFirestore

/// User preferences for ingredient scanner app
///
/// Contains both app settings and recipe generation preferences:
/// - User settings (theme, notifications, user info)
/// - Dietary restrictions (allergies, dietary choices)
/// - Family size (for portion planning)
/// - Preference to respect restrictions in recipe generation
///
/// Enhanced for Firestore compatibility with proper Codable support
struct UserPreferences: Codable, Equatable, Sendable {

    // MARK: - Firestore Integration

    /// Document ID for Firestore (automatically managed by Firebase)
    var documentID: String?

    // MARK: - User Settings (Task 9 Requirements)

    /// User ID for Firestore sync
    var userId: String

    /// App theme setting
    var theme: String

    /// Notification preferences
    var notifications: Bool

    /// Last updated timestamp
    var lastUpdated: Date

    /// Created timestamp for new users
    var createdAt: Date

    // MARK: - Family Information

    /// Number of people in household (for recipe servings)
    var familySize: Int


    /// Number of adults in household (must be at least 1)
    var numberOfAdults: Int

    /// Number of kids in household (under 16)
    var numberOfKids: Int
    /// Family member names and ages (for portion customization)
    var familyMembers: [FamilyMember]

    // MARK: - Recipe Preferences (Enhanced)

    /// Dietary restrictions and allergies
    var dietaryRestrictions: [DietaryRestriction]

    /// Strict exclusions (foods to never include)
    var strictExclusions: [StrictExclusion]

    /// Allergies and intolerances (medical restrictions)
    var allergiesIntolerances: [AllergyIntolerance]

    /// Whether to respect dietary restrictions in recipe generation
    var respectRestrictions: Bool

    // MARK: - Equipment & Cookware (Phase 2)

    /// Kitchen equipment and cookware owned by user
    var equipmentOwned: [String]

    // MARK: - Computed Properties

    /// Returns true if user has any dietary restrictions
    var hasRestrictions: Bool {
        return !dietaryRestrictions.isEmpty || !strictExclusions.isEmpty || !allergiesIntolerances.isEmpty
    }

    /// Returns formatted string of dietary restrictions for display
    var restrictionsDisplayText: String {
        let allRestrictions = dietaryRestrictions.map { $0.rawValue } +
                            strictExclusions.map { $0.rawValue } +
                            allergiesIntolerances.map { $0.rawValue }
        if allRestrictions.isEmpty {
            return "None"
        }
        return allRestrictions.joined(separator: ", ")
    }

    /// Returns count of all restrictions for UI display
    var restrictionsCount: Int {
        return dietaryRestrictions.count + strictExclusions.count + allergiesIntolerances.count
    }

    /// Returns count of active allergies
    var allergiesCount: Int {
        return allergiesIntolerances.count
    }

    // MARK: - Backward Compatibility Properties

    /// Backward compatibility: returns allergies as string array
    var allergies: [String] {
        return allergiesIntolerances.map { $0.rawValue }
    }

    /// Backward compatibility: returns intolerances as string array
    var intolerances: [String] {
        return allergiesIntolerances.map { $0.rawValue }
    }

    // MARK: - Firestore Codable Implementation

    enum CodingKeys: String, CodingKey {
        case documentID
        case userId
        case theme
        case notifications
        case lastUpdated
        case createdAt
        case familySize
        case numberOfAdults
        case numberOfKids
        case familyMembers
        case dietaryRestrictions
        case strictExclusions
        case allergiesIntolerances
        case respectRestrictions
        case equipmentOwned
    }

    /// Initialize from Firestore decoder
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // Handle documentID separately (managed by Firebase)
        documentID = try container.decodeIfPresent(String.self, forKey: .documentID)

        userId = try container.decode(String.self, forKey: .userId)
        theme = try container.decodeIfPresent(String.self, forKey: .theme) ?? "system"
        notifications = try container.decodeIfPresent(Bool.self, forKey: .notifications) ?? true
        lastUpdated = try container.decodeIfPresent(Date.self, forKey: .lastUpdated) ?? Date()
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? Date()
        familySize = try container.decodeIfPresent(Int.self, forKey: .familySize) ?? 2
        // New fields with backward compatibility
        let decodedAdults = try container.decodeIfPresent(Int.self, forKey: .numberOfAdults)
        let decodedKids = try container.decodeIfPresent(Int.self, forKey: .numberOfKids)
        if let adults = decodedAdults, let kids = decodedKids {
            numberOfAdults = max(1, adults)
            numberOfKids = max(0, kids)
        } else {
            // Derive from legacy familySize: at least 1 adult, remaining are kids
            numberOfAdults = max(1, min(familySize, 1))
            numberOfKids = max(0, familySize - numberOfAdults)
        }
        familyMembers = try container.decodeIfPresent([FamilyMember].self, forKey: .familyMembers) ?? []
        dietaryRestrictions = try container.decodeIfPresent([DietaryRestriction].self, forKey: .dietaryRestrictions) ?? []
        strictExclusions = try container.decodeIfPresent([StrictExclusion].self, forKey: .strictExclusions) ?? []
        allergiesIntolerances = try container.decodeIfPresent([AllergyIntolerance].self, forKey: .allergiesIntolerances) ?? []
        respectRestrictions = try container.decodeIfPresent(Bool.self, forKey: .respectRestrictions) ?? true
        equipmentOwned = try container.decodeIfPresent([String].self, forKey: .equipmentOwned) ?? []
    }

    /// Encode to Firestore encoder
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        // documentID is handled automatically by Firebase
        try container.encodeIfPresent(documentID, forKey: .documentID)

        // Keep familySize synchronized as adults + kids for backward compatibility
        let total = max(1, numberOfAdults) + max(0, numberOfKids)
        try container.encode(userId, forKey: .userId)
        try container.encode(theme, forKey: .theme)
        try container.encode(notifications, forKey: .notifications)
        try container.encode(lastUpdated, forKey: .lastUpdated)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(total, forKey: .familySize)
        try container.encode(numberOfAdults, forKey: .numberOfAdults)
        try container.encode(numberOfKids, forKey: .numberOfKids)
        try container.encode(familyMembers, forKey: .familyMembers)
        try container.encode(dietaryRestrictions, forKey: .dietaryRestrictions)
        try container.encode(strictExclusions, forKey: .strictExclusions)
        try container.encode(allergiesIntolerances, forKey: .allergiesIntolerances)
        try container.encode(respectRestrictions, forKey: .respectRestrictions)
        try container.encode(equipmentOwned, forKey: .equipmentOwned)
    }




    /// Standard initializer for creating new user preferences
    init(
        userId: String,
        theme: String = "system",
        notifications: Bool = true,
        lastUpdated: Date = Date(),
        createdAt: Date = Date(),
        familySize: Int = 2,
        numberOfAdults: Int = 1,
        numberOfKids: Int = 1,
        familyMembers: [FamilyMember] = [],
        dietaryRestrictions: [DietaryRestriction] = [],
        strictExclusions: [StrictExclusion] = [],
        allergiesIntolerances: [AllergyIntolerance] = [],
        respectRestrictions: Bool = true,
        equipmentOwned: [String] = []
    ) {
        self.documentID = nil
        self.userId = userId
        self.theme = theme
        self.notifications = notifications
        self.lastUpdated = lastUpdated
        self.createdAt = createdAt
        self.familySize = max(1, numberOfAdults) + max(0, numberOfKids)
        self.numberOfAdults = max(1, numberOfAdults)
        self.numberOfKids = max(0, numberOfKids)
        self.familyMembers = familyMembers
        self.dietaryRestrictions = dietaryRestrictions
        self.strictExclusions = strictExclusions
        self.allergiesIntolerances = allergiesIntolerances
        self.respectRestrictions = respectRestrictions
        self.equipmentOwned = equipmentOwned
    }
}

// MARK: - Family Member Model

struct FamilyMember: Codable, Equatable, Identifiable, Sendable {
    let id: UUID
    var name: String
    var age: Int?
    var specialDiets: [DietaryRestriction]

    init(name: String, age: Int? = nil, specialDiets: [DietaryRestriction] = []) {
        self.id = UUID()
        self.name = name
        self.age = age
        self.specialDiets = specialDiets
    }

    // MARK: - Firestore Codable Implementation

    enum CodingKeys: String, CodingKey {
        case id, name, age, specialDiets
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        age = try container.decodeIfPresent(Int.self, forKey: .age)
        specialDiets = try container.decodeIfPresent([DietaryRestriction].self, forKey: .specialDiets) ?? []
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encodeIfPresent(age, forKey: .age)
        try container.encode(specialDiets, forKey: .specialDiets)
    }
}

// MARK: - Dietary Restrictions (Enhanced)

enum DietaryRestriction: String, CaseIterable, Codable, Sendable {

    // MARK: - Dietary Preferences
    case vegan = "Vegan"
    case vegetarian = "Vegetarian"
    case lactoOvoVegetarian = "Lacto-Ovo Vegetarian"
    case pescatarian = "Pescatarian"
    case paleo = "Paleo"
    case keto = "Keto"
    case lowCarb = "Low-Carb"
    case lowSodium = "Low-Sodium"
    case lowFat = "Low-Fat"
    case lowFODMAP = "Low-FODMAP"
    case diabeticFriendly = "Diabetic-Friendly"
    case halal = "Halal"
    case kosher = "Kosher"

    // MARK: - Display Properties

    /// Category for grouping in UI
    var category: RestrictionCategory {
        return .dietary
    }

    /// Icon for UI display
    var icon: String {
        switch self {
        case .vegetarian, .vegan, .lactoOvoVegetarian: return "🥬"
        case .pescatarian: return "🐟"
        case .keto, .lowCarb: return "🥩"
        case .paleo: return "🍖"
        case .lowSodium: return "🧂"
        case .lowFat: return "🥗"
        case .lowFODMAP: return "🥦"
        case .diabeticFriendly: return "💙"
        case .halal: return "🕌"
        case .kosher: return "✡️"
        }
    }
}

// MARK: - Strict Exclusions

enum StrictExclusion: String, CaseIterable, Codable, Sendable {
    case alcohol = "Alcohol"
    case artificialSweeteners = "Artificial Sweeteners"
    case beef = "Beef"
    case caffeine = "Caffeine"
    case cilantro = "Cilantro / Coriander"
    case gameMeats = "Game Meats"
    case garlic = "Garlic"
    case lamb = "Lamb"
    case mushrooms = "Mushrooms"
    case onions = "Onions"
    case offal = "Offal / Organ Meats"
    case pork = "Pork"
    case poultry = "Poultry"
    case redMeat = "Red Meat"
    case spicyFood = "Spicy Food"

    var icon: String {
        switch self {
        case .beef, .pork, .lamb, .redMeat: return "🥩"
        case .poultry: return "🍗"
        case .gameMeats: return "🦌"
        case .garlic: return "🧄"
        case .onions: return "🧅"
        case .mushrooms: return "🍄"
        case .cilantro: return "🌿"
        case .spicyFood: return "🌶️"
        case .alcohol: return "🍷"
        case .caffeine: return "☕"
        case .artificialSweeteners: return "🧪"
        case .offal: return "🥩"
        }
    }
}

// MARK: - Allergies and Intolerances (Simplified)

enum AllergyIntolerance: String, CaseIterable, Codable, Sendable {
    // Medical allergies and intolerances
    case celery = "Celery"
    case corn = "Corn"
    case crustaceanShellfish = "Crustacean Shellfish"
    case eggs = "Eggs"
    case fish = "Fish"
    case gluten = "Gluten"
    case lactose = "Lactose"
    case milk = "Milk"
    case mustard = "Mustard"
    case peanuts = "Peanuts"
    case sesame = "Sesame"
    case soybeans = "Soybeans"
    case sulfites = "Sulfites"
    case treeNuts = "Tree Nuts"
    case wheat = "Wheat"

    var icon: String {
        switch self {
        case .milk: return "🥛"
        case .eggs: return "🥚"
        case .fish: return "🐟"
        case .crustaceanShellfish: return "🦐"
        case .treeNuts, .peanuts: return "🥜"
        case .wheat, .gluten: return "🌾"
        case .soybeans: return "🫘"
        case .sesame: return "🫘"
        case .lactose: return "🥛"
        case .sulfites: return "🍷"
        case .corn: return "🌽"
        case .mustard: return "🌭"
        case .celery: return "🥬"
        }
    }
}

// MARK: - Restriction Categories (Enhanced)

enum RestrictionCategory: String, CaseIterable, Sendable {
    case dietary = "Dietary Restrictions"
    case strictExclusions = "Strict Exclusions"
    case allergies = "Allergies & Intolerances"
    case family = "Family Information"

    var icon: String {
        switch self {
        case .dietary: return "🥗"
        case .strictExclusions: return "🚫"
        case .allergies: return "⚠️"
        case .family: return "👨‍👩‍👧‍👦"
        }
    }

    var description: String {
        switch self {
        case .dietary: return "Foods you prefer to eat or avoid"
        case .strictExclusions: return "Foods you never want to see in recipes"
        case .allergies: return "Medical allergies and food intolerances"
        case .family: return "Information about household members"
        }
    }
}

// MARK: - Default Values & Firestore Helpers

extension UserPreferences {

    /// Default preferences for new users - Firestore compatible
    static func createDefault(for userId: String) -> UserPreferences {
        return UserPreferences(
            userId: userId,
            theme: "system",
            notifications: true,
            lastUpdated: Date(),
            createdAt: Date(),
            familySize: 2,
            numberOfAdults: 1,
            numberOfKids: 1,
            familyMembers: [],
            dietaryRestrictions: [],
            strictExclusions: [],
            allergiesIntolerances: [],
            respectRestrictions: true,
            equipmentOwned: []
        )
    }

    /// Legacy default preferences (for backward compatibility)
    @MainActor
    static let `default` = UserPreferences(
        userId: "",
        theme: "system",
        notifications: true,
        lastUpdated: Date(),
        createdAt: Date(),
        familySize: 2,
        numberOfAdults: 1,
        numberOfKids: 1,
        familyMembers: [],
        dietaryRestrictions: [],
        strictExclusions: [],
        allergiesIntolerances: [],
        respectRestrictions: true,
        equipmentOwned: []
    )

    /// Sample preferences for testing/preview - Firestore compatible
    @MainActor
    static let sample = UserPreferences(
        userId: "sample_user_123",
        theme: "dark",
        notifications: false,
        lastUpdated: Date(),
        createdAt: Date(),
        familySize: 4,
        numberOfAdults: 2,
        numberOfKids: 2,
        familyMembers: [
            FamilyMember(name: "John", age: 35),
            FamilyMember(name: "Jane", age: 32),
            FamilyMember(name: "Emma", age: 8, specialDiets: [.vegetarian]),
            FamilyMember(name: "Liam", age: 5)
        ],
        dietaryRestrictions: [.vegetarian, .lowSodium],
        strictExclusions: [.pork, .beef],
        allergiesIntolerances: [.treeNuts, .milk],
        respectRestrictions: true,
        equipmentOwned: ["oven", "stovetop", "microwave", "blender", "air fryer"]
    )

    /// Update the lastUpdated timestamp to current time
    mutating func updateTimestamp() {
        lastUpdated = Date()
    }

    /// Validate that all required fields are present for Firestore
    func isValidForFirestore() -> Bool {
        return !userId.isEmpty
    }

    /// Create a copy with updated timestamp
    func withUpdatedTimestamp() -> UserPreferences {
        var updated = self
        updated.lastUpdated = Date()
        return updated
    }
}
