import Foundation

// MARK: - UI Mode

enum UIMode: String, CaseIterable, Codable {
    case quick
    case custom
}

// MARK: - Meal Types (aligned with PRD)

enum MealType: String, CaseIterable, Codable, Sendable {
    case breakfast = "breakfast"
    case lunch = "lunch"
    case dinner = "dinner"

    var displayName: String {
        switch self {
        case .breakfast: return NSLocalizedString("meal_breakfast", comment: "Breakfast meal type")
        case .lunch: return NSLocalizedString("meal_lunch", comment: "Lunch meal type")
        case .dinner: return NSLocalizedString("meal_dinner", comment: "Dinner meal type")
        }
    }
}

// MARK: - Configurations (PRD v3)

struct MealConfig: Codable, Equatable, Sendable {
    var cookingTimeMinutes: Int = 30 // 5–120
    var numberOfDishes: Int = 2      // 1–6
}

/// Multi-day Meal Prep configuration (formerly CustomConfiguration extras trimmed per PRD v3)
struct CustomConfiguration: Equatable, Sendable {
    var selectedMeals: Set<MealType> = []
    var mealConfigurations: [MealType: MealConfig] = [:]
    var days: Int = 3
    /// Selected cuisines (multi-select)
    var cuisines: [String] = []
    /// Additional request notes
    var additionalRequest: String = ""
}

/// Quick mode configuration (PRD v3 Quick)
struct QuickConfiguration: Equatable, Sendable {
    var mealType: MealType = .dinner
    var numberOfDishes: Int = 1 // 1–6
    var totalTimeMinutes: Int = 30 // 5–120, step 5
    var cuisines: [String] = []
    var additionalRequest: String = ""
}

