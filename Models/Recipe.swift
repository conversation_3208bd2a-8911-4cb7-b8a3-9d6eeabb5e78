import Foundation

struct RecipePreferences: Sendable {
    var cookingTimeInMinutes: Int
    var numberOfServings: Int
    var dietaryRestrictions: [String] // e.g., ["Vegetarian", "Gluten-Free"]
    var allergiesAndIntolerances: [String] // e.g., ["egg", "peanut"]
    var strictExclusions: [String] // e.g., ["pork", "alcohol"]
    var respectRestrictions: Bool
    // Phase 1 additions
    var cuisines: [String] = []
    var additionalRequest: String? = nil
    var equipmentOwned: [String] = []

    /// Initialize from UserPreferences
    init(from userPreferences: UserPreferences, cookingTime: Int) {
        self.cookingTimeInMinutes = cookingTime
        self.numberOfServings = userPreferences.familySize
        self.dietaryRestrictions = userPreferences.dietaryRestrictions.map { $0.rawValue }
        self.allergiesAndIntolerances = userPreferences.allergiesIntolerances.map { $0.rawValue }
        self.strictExclusions = userPreferences.strictExclusions.map { $0.rawValue }
        self.respectRestrictions = userPreferences.respectRestrictions
        // other fields remain defaults; adapter may override
    }

    /// Manual initialization
    init(
        cookingTimeInMinutes: Int,
        numberOfServings: Int,
        dietaryRestrictions: [String],
        allergiesAndIntolerances: [String] = [],
        strictExclusions: [String] = [],
        respectRestrictions: Bool = true,
        cuisines: [String] = [],
        additionalRequest: String? = nil,
        equipmentOwned: [String] = []
    ) {
        self.cookingTimeInMinutes = cookingTimeInMinutes
        self.numberOfServings = numberOfServings
        self.dietaryRestrictions = dietaryRestrictions
        self.allergiesAndIntolerances = allergiesAndIntolerances
        self.strictExclusions = strictExclusions
        self.respectRestrictions = respectRestrictions
        self.cuisines = cuisines
        self.additionalRequest = additionalRequest
        self.equipmentOwned = equipmentOwned
    }
}

struct Recipe: Codable, Identifiable, Hashable, Sendable {
    let id = UUID()
    let recipeTitle: String
    let description: String
    let ingredients: [String]
    let instructions: [String]
    let nutrition: NutritionInfo
    let cookingTime: String
    let servings: Int
    let difficulty: Difficulty
    
    // Computed properties for compatibility
    var title: String {
        return recipeTitle
    }
    
    var cookingTimeInMinutes: Int {
        // Extract minutes from cookingTime string (e.g., "30 minutes" -> 30)
        let components = cookingTime.components(separatedBy: CharacterSet.decimalDigits.inverted)
        return Int(components.first(where: { !$0.isEmpty }) ?? "30") ?? 30
    }
    
    var numberOfServings: Int {
        return servings
    }
    
    // Placeholder tags for compatibility (can be derived from ingredients/description)
    var tags: [String] {
        var derivedTags: [String] = []
        
        // Add difficulty as tag
        derivedTags.append(difficulty.rawValue)
        
        // Add cooking time category
        let minutes = cookingTimeInMinutes
        if minutes <= 30 {
            derivedTags.append("quick")
        } else if minutes <= 60 {
            derivedTags.append("moderate")
        } else {
            derivedTags.append("slow")
        }
        
        // Add serving size category
        if servings <= 2 {
            derivedTags.append("small-batch")
        } else if servings <= 4 {
            derivedTags.append("family")
        } else {
            derivedTags.append("large-batch")
        }
        
        return derivedTags
    }
    
    enum Difficulty: String, Codable, CaseIterable, Sendable {
        case easy = "easy"
        case medium = "medium"
        case hard = "hard"
    }
    
    enum CodingKeys: String, CodingKey {
        case recipeTitle, description, ingredients, instructions, nutrition, cookingTime, servings, difficulty
    }
    
    struct NutritionInfo: Codable, Hashable, Sendable {
        let calories: String
        let protein: String
        let carbs: String
        let fat: String
    }
    
    // Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    // Equatable conformance (using id for comparison)
    static func == (lhs: Recipe, rhs: Recipe) -> Bool {
        return lhs.id == rhs.id
    }
}

struct RecipeIdea: Identifiable, Sendable {
    let id = UUID()
    let recipe: Recipe
    var status: RecipeStatus
    var missingIngredients: [String]
    
    enum RecipeStatus: Sendable {
        case readyToCook
        case almostThere
    }
}

struct RecipeListResponse: Codable, Sendable {
    let recipes: [Recipe]
}